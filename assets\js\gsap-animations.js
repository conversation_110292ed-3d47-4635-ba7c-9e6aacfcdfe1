// VTA GSAP Animations - Sci-Fi Hero Section
// Adapted from test.js with VTA yellow/gold theme

const vtaColors = {
    gold: '#FFD700',
    orange: '#FF8C00',
    yellow: '#FFA500',
    dark: '#1a1a1a',
    light: '#ffffff',
    accent: '#f39c12'
};

// Utility functions
const $ = sel => document.querySelector(sel);
const $$ = sel => document.querySelectorAll(sel);

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS
    AOS.init({
        duration: 1000,
        once: true,
        offset: 100
    });
    
    // Initialize GSAP animations
    initHeroAnimations();
    initStarfield();
});

function initHeroAnimations() {
    const heroSection = $('.hero');
    if (!heroSection) return;
    
    // Add starfield container
    const starfieldContainer = document.createElement('div');
    starfieldContainer.id = 'starfield';
    starfieldContainer.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 0;
        pointer-events: none;
    `;
    heroSection.insertBefore(starfieldContainer, heroSection.firstChild);
    
    // Add UI elements
    addUIElements();
    
    // Start intro animation
    setTimeout(() => {
        introAnimation();
    }, 500);
}

function addUIElements() {
    const heroSection = $('.hero');
    
    // Create UI overlay
    const uiOverlay = document.createElement('div');
    uiOverlay.className = 'vta-ui-overlay';
    uiOverlay.innerHTML = `
        <!-- Top border -->
        <div class="ui-border-top">
            <svg viewBox="0 0 620 30" class="border-svg">
                <path d="M0,15 L580,15 M20,5 L600,5 M20,25 L600,25" stroke="${vtaColors.gold}" stroke-width="2" fill="none" opacity="0.6"></path>
            </svg>
            <div class="ui-battery"></div>
        </div>
        
        <!-- Side borders -->
        <div class="ui-border-left">
            <div class="ui-cap top"></div>
            <div class="ui-line"></div>
            <div class="ui-cap bottom"></div>
        </div>
        <div class="ui-border-right">
            <div class="ui-cap top"></div>
            <div class="ui-line"></div>
            <div class="ui-cap bottom"></div>
        </div>
        
        <!-- Corner elements -->
        <div class="ui-corners">
            <div class="ui-corner top-left"></div>
            <div class="ui-corner top-right"></div>
            <div class="ui-corner bottom-left"></div>
            <div class="ui-corner bottom-right"></div>
        </div>
        
        <!-- Status indicators -->
        <div class="ui-status left">
            <h5 class="status-title">VTA Academy</h5>
            <div class="status-bars">
                <div class="status-bar"></div>
                <div class="status-bar"></div>
                <div class="status-bar"></div>
            </div>
        </div>
        <div class="ui-status right">
            <h5 class="status-title">Learning Mode</h5>
            <div class="status-bars">
                <div class="status-bar"></div>
                <div class="status-bar"></div>
                <div class="status-bar"></div>
            </div>
        </div>
        
        <!-- Center crosshair -->
        <div class="ui-crosshair">
            <div class="crosshair-line horizontal"></div>
            <div class="crosshair-line vertical"></div>
            <div class="crosshair-dot top"></div>
            <div class="crosshair-dot bottom"></div>
        </div>
    `;
    
    heroSection.appendChild(uiOverlay);
    
    // Add CSS for UI elements
    addUIStyles();
}

function addUIStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .vta-ui-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            opacity: 0;
        }
        
        .ui-border-top {
            position: absolute;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            width: 80%;
            height: 30px;
        }
        
        .border-svg {
            width: 100%;
            height: 100%;
        }
        
        .ui-battery {
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 12px;
            height: 4px;
            background: ${vtaColors.orange};
            box-shadow: 0 0 10px ${vtaColors.orange};
        }
        
        .ui-border-left, .ui-border-right {
            position: absolute;
            top: 20%;
            height: 60%;
            width: 3px;
        }
        
        .ui-border-left {
            left: 5%;
        }
        
        .ui-border-right {
            right: 5%;
        }
        
        .ui-line {
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, transparent, ${vtaColors.gold}, transparent);
            opacity: 0.6;
        }
        
        .ui-cap {
            position: absolute;
            width: 8px;
            height: 8px;
            left: -2.5px;
            border: 2px solid ${vtaColors.orange};
        }
        
        .ui-cap.top {
            top: -4px;
            border-bottom: none;
        }
        
        .ui-cap.bottom {
            bottom: -4px;
            border-top: none;
        }
        
        .ui-corner {
            position: absolute;
            width: 20px;
            height: 20px;
            border: 2px solid ${vtaColors.gold};
            opacity: 0.7;
        }
        
        .ui-corner.top-left {
            top: 80px;
            left: 80px;
            border-right: none;
            border-bottom: none;
        }
        
        .ui-corner.top-right {
            top: 80px;
            right: 80px;
            border-left: none;
            border-bottom: none;
        }
        
        .ui-corner.bottom-left {
            bottom: 80px;
            left: 80px;
            border-right: none;
            border-top: none;
        }
        
        .ui-corner.bottom-right {
            bottom: 80px;
            right: 80px;
            border-left: none;
            border-top: none;
        }
        
        .ui-status {
            position: absolute;
            bottom: 100px;
            width: 120px;
        }
        
        .ui-status.left {
            left: 80px;
        }
        
        .ui-status.right {
            right: 80px;
        }
        
        .status-title {
            color: ${vtaColors.gold};
            font-size: 10px;
            text-transform: uppercase;
            letter-spacing: 2px;
            margin-bottom: 10px;
            opacity: 0;
        }
        
        .status-bars {
            display: flex;
            gap: 3px;
        }
        
        .status-bar {
            width: 30px;
            height: 3px;
            background: ${vtaColors.gold};
            opacity: 0;
        }
        
        .ui-crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100px;
            height: 100px;
            opacity: 0.3;
        }
        
        .crosshair-line {
            position: absolute;
            background: ${vtaColors.light};
        }
        
        .crosshair-line.horizontal {
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
        }
        
        .crosshair-line.vertical {
            top: 0;
            bottom: 0;
            left: 50%;
            width: 1px;
        }
        
        .crosshair-dot {
            position: absolute;
            width: 6px;
            height: 6px;
            background: ${vtaColors.orange};
            border-radius: 50%;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .crosshair-dot.top {
            top: 0;
        }
        
        .crosshair-dot.bottom {
            bottom: 0;
        }
        
        @media (max-width: 768px) {
            .ui-border-left { left: 2%; }
            .ui-border-right { right: 2%; }
            .ui-corner.top-left, .ui-corner.bottom-left { left: 20px; }
            .ui-corner.top-right, .ui-corner.bottom-right { right: 20px; }
            .ui-status.left { left: 20px; }
            .ui-status.right { right: 20px; }
        }
    `;
    document.head.appendChild(style);
}

function introAnimation() {
    const duration = 4;
    const heroContent = $('.hero-content');
    const typingText = $('.typing-text');
    const uiOverlay = $('.vta-ui-overlay');
    const scrollIndicator = $('.scroll-indicator');

    // Set initial states
    gsap.set([uiOverlay, heroContent], { autoAlpha: 0 });
    gsap.set('.ui-border-top', { y: -50 });
    gsap.set(['.ui-border-left', '.ui-border-right'], { scaleY: 0 });
    gsap.set('.ui-corner', { scale: 0 });
    gsap.set('.status-title', { autoAlpha: 0 });
    gsap.set('.status-bar', { scaleX: 0 });
    gsap.set('.ui-crosshair', { scale: 0, rotation: 45 });

    // Create main timeline
    const tl = gsap.timeline({
        onComplete: () => {
            document.body.classList.add('vta-intro-complete');
        }
    });

    // UI Border animations
    tl.to(uiOverlay, 0.5, { autoAlpha: 1 })
      .from('.ui-border-top', duration * 0.3, {
          y: -50,
          autoAlpha: 0,
          ease: "elastic.out(1, 0.8)"
      }, 0.5)
      .to(['.ui-border-left', '.ui-border-right'], duration * 0.4, {
          scaleY: 1,
          ease: "elastic.out(1, 0.8)"
      }, 0.7)
      .to('.ui-corner', duration * 0.3, {
          scale: 1,
          stagger: 0.1,
          ease: "back.out(1.7)"
      }, 1.0)

      // Crosshair animation
      .to('.ui-crosshair', duration * 0.4, {
          scale: 1,
          rotation: 0,
          autoAlpha: 0.3,
          ease: "elastic.out(1, 0.8)"
      }, 1.2)

      // Status indicators
      .to('.status-title', 0.8, {
          autoAlpha: 1,
          stagger: 0.2,
          ease: "power2.out"
      }, 1.5)
      .to('.status-bar', 1.2, {
          scaleX: 1,
          stagger: 0.1,
          ease: "power2.out"
      }, 1.7)

      // Hero content
      .to(heroContent, 1, {
          autoAlpha: 1,
          y: 0,
          ease: "power2.out"
      }, 2.0)

      // Typing animation for title
      .from(typingText, 1.5, {
          text: "",
          ease: "none"
      }, 2.5)

      // Scroll indicator
      .from(scrollIndicator, 0.8, {
          y: 30,
          autoAlpha: 0,
          ease: "back.out(1.7)"
      }, 3.5);

    // Continuous animations
    startContinuousAnimations();
}

function startContinuousAnimations() {
    // Pulsing battery
    gsap.to('.ui-battery', 2, {
        boxShadow: `0 0 20px ${vtaColors.orange}`,
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut"
    });

    // Flickering status bars
    gsap.to('.status-bar', 3, {
        opacity: 0.3,
        stagger: 0.2,
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut"
    });

    // Rotating crosshair
    gsap.to('.ui-crosshair', 20, {
        rotation: 360,
        repeat: -1,
        ease: "none"
    });
}

function initStarfield() {
    const starfieldContainer = $('#starfield');
    if (!starfieldContainer || !window.THREE) return;

    // Use enhanced starfield with VTA colors and better performance
    createEnhancedStarfield(starfieldContainer);
}

function createStarfield(container) {
    let scene, camera, renderer, stars;
    let mouseX = 0, mouseY = 0;

    const WIDTH = window.innerWidth;
    const HEIGHT = window.innerHeight;

    // Scene setup
    scene = new THREE.Scene();
    camera = new THREE.PerspectiveCamera(75, WIDTH / HEIGHT, 1, 1000);
    camera.position.z = 500;

    // Renderer setup
    renderer = new THREE.WebGLRenderer({ alpha: true });
    renderer.setSize(WIDTH, HEIGHT);
    renderer.setClearColor(0x000000, 0);
    container.appendChild(renderer.domElement);

    // Create stars
    const starGeometry = new THREE.BufferGeometry();
    const starCount = 1000;
    const positions = new Float32Array(starCount * 3);

    for (let i = 0; i < starCount * 3; i += 3) {
        positions[i] = (Math.random() - 0.5) * 2000;     // x
        positions[i + 1] = (Math.random() - 0.5) * 2000; // y
        positions[i + 2] = (Math.random() - 0.5) * 1000; // z
    }

    starGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

    const starMaterial = new THREE.PointsMaterial({
        color: 0xFFD700,
        size: 2,
        transparent: true,
        opacity: 0.8
    });

    stars = new THREE.Points(starGeometry, starMaterial);
    scene.add(stars);

    // Mouse movement
    document.addEventListener('mousemove', (event) => {
        mouseX = (event.clientX - WIDTH / 2) * 0.0005;
        mouseY = (event.clientY - HEIGHT / 2) * 0.0005;
    });

    // Animation loop
    function animate() {
        requestAnimationFrame(animate);

        stars.rotation.x += mouseY * 0.1;
        stars.rotation.y += mouseX * 0.1;

        renderer.render(scene, camera);
    }

    animate();

    // Handle resize
    window.addEventListener('resize', () => {
        const newWidth = window.innerWidth;
        const newHeight = window.innerHeight;

        camera.aspect = newWidth / newHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(newWidth, newHeight);
    });
}

// Additional utility functions from test.js
function resetStyle(selector, callback) {
    if (typeof selector === 'string') {
        Array.from(document.querySelectorAll(selector)).forEach((c, i) => c.style = null);
    } else {
        if (callback) {
            Array.from(selector).forEach(callback);
        } else {
            Array.from(selector).forEach((c, i) => c.style = null);
        }
    }
}

function colorHex(color) {
    return parseInt('0x' + color.replace(/#/g, '').toUpperCase());
}

// Enhanced starfield with VTA colors
function createEnhancedStarfield(container) {
    let scene, camera, renderer, stars;
    let mouseX = 0, mouseY = 0;

    const WIDTH = window.innerWidth;
    const HEIGHT = window.innerHeight;
    const aspectRatio = WIDTH / HEIGHT;
    const fieldOfView = 75;
    const nearPlane = 1;
    const farPlane = 5000;

    // Scene setup
    scene = new THREE.Scene({ antialias: true });
    camera = new THREE.PerspectiveCamera(fieldOfView, aspectRatio, nearPlane, farPlane);

    // Renderer setup with WebGL support detection
    renderer = webGLSupport() ? new THREE.WebGLRenderer({ alpha: true }) : new THREE.CanvasRenderer({ alpha: true });
    renderer.setClearColor(new THREE.Color(colorHex(vtaColors.dark)), 0);
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.setSize(WIDTH, HEIGHT);

    // Create stars with VTA colors
    const geometry = new THREE.BufferGeometry();
    const starCount = 1500;
    const positions = new Float32Array(starCount * 3);
    const colors = new Float32Array(starCount * 3);

    for (let i = 0; i < starCount * 3; i += 3) {
        // Position
        positions[i] = (Math.random() - 0.5) * farPlane;     // x
        positions[i + 1] = (Math.random() - 0.5) * farPlane; // y
        positions[i + 2] = (Math.random() - 0.5) * farPlane; // z

        // Colors - mix of VTA gold, orange, yellow
        const colorChoice = Math.random();
        if (colorChoice < 0.4) {
            // Gold
            colors[i] = 1.0;     // r
            colors[i + 1] = 0.84; // g
            colors[i + 2] = 0.0;  // b
        } else if (colorChoice < 0.7) {
            // Orange
            colors[i] = 1.0;     // r
            colors[i + 1] = 0.55; // g
            colors[i + 2] = 0.0;  // b
        } else {
            // Yellow
            colors[i] = 1.0;     // r
            colors[i + 1] = 0.65; // g
            colors[i + 2] = 0.0;  // b
        }
    }

    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

    const starMaterial = new THREE.PointsMaterial({
        size: 3,
        vertexColors: true,
        transparent: true,
        opacity: 0.8
    });

    stars = new THREE.Points(geometry, starMaterial);
    scene.add(stars);

    // Initialize
    container.appendChild(renderer.domElement);
    camera.position.z = 1070 * 2;

    // Event listeners
    window.addEventListener('resize', onWindowResize, false);
    document.addEventListener('mousemove', onMouseMove, false);

    // Animation loop
    function animate() {
        requestAnimationFrame(animate);
        render();
    }

    function render() {
        stars.rotation.x += (mouseY - stars.rotation.x) * 0.00002;
        stars.rotation.y += (mouseX - stars.rotation.y) * 0.00002;
        camera.lookAt(scene.position);
        renderer.render(scene, camera);
    }

    function webGLSupport() {
        try {
            const canvas = document.createElement('canvas');
            return !!(window.WebGLRenderingContext &&
                (canvas.getContext('webgl') ||
                 canvas.getContext('experimental-webgl'))
            );
        } catch(e) {
            return false;
        }
    }

    function onWindowResize() {
        const w = window.innerWidth;
        const h = window.innerHeight;
        camera.aspect = w / h;
        camera.updateProjectionMatrix();
        renderer.setSize(w, h);
    }

    function onMouseMove(e) {
        const windowHalfX = WIDTH / 2;
        const windowHalfY = HEIGHT / 2;
        mouseX = e.clientX - windowHalfX;
        mouseY = e.clientY - windowHalfY;
    }

    animate();

    return { stars, scene, camera, renderer };
}
