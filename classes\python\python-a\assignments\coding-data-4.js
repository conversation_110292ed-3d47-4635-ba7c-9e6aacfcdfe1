// <PERSON><PERSON> liệu bài tập luyện tập code - Bài 4: <PERSON><PERSON> và <PERSON>ậ<PERSON>/Xu<PERSON>t D<PERSON>u
const codingProblems = [
    {
        id: 1,
        title: "<PERSON>ào Hỏi Tương Tác",
        difficulty: "<PERSON><PERSON>",
        filename: "chao_hoi_tuong_tac.py",
        description: "Viết chương trình hỏi tên người dùng, sau đó in ra lời chào kèm theo tên của họ.",
        objective: "Luyện tập input(), biến chuỗi, và nối chuỗi cơ bản.",
        sampleCode: `# 1. Nhap du lieu
ten_nguoi_dung = input("Moi ban nhap ten: ")

# 2. Xu ly va in ket qua
loi_chao = "Xin chao ban " + ten_nguoi_dung + ", chuc ban mot ngay tot lanh!"
print(loi_chao)`,
        testcases: [
            {
                input: ["An"],
                expectedOutput: "<PERSON><PERSON> chao ban <PERSON>, chuc ban mot ngay tot lanh!"
            },
            {
                input: ["Minh"],
                expectedOutput: "<PERSON><PERSON> chao ban <PERSON>, chuc ban mot ngay tot lanh!"
            },
            {
                input: ["Linh"],
                expectedOutput: "Xin chao ban Linh, chuc ban mot ngay tot lanh!"
            },
            {
                input: ["Duc"],
                expectedOutput: "Xin chao ban Duc, chuc ban mot ngay tot lanh!"
            },
            {
                input: ["Hoa"],
                expectedOutput: "Xin chao ban Hoa, chuc ban mot ngay tot lanh!"
            },
            {
                input: ["Nam"],
                expectedOutput: "Xin chao ban Nam, chuc ban mot ngay tot lanh!"
            },
            {
                input: ["Trang"],
                expectedOutput: "Xin chao ban Trang, chuc ban mot ngay tot lanh!"
            },
            {
                input: ["Quan"],
                expectedOutput: "Xin chao ban Quan, chuc ban mot ngay tot lanh!"
            },
            {
                input: ["Vy"],
                expectedOutput: "Xin chao ban Vy, chuc ban mot ngay tot lanh!"
            },
            {
                input: ["Khang"],
                expectedOutput: "Xin chao ban Khang, chuc ban mot ngay tot lanh!"
            }
        ]
    },
    {
        id: 2,
        title: "Máy Tính Cộng Đơn Giản",
        difficulty: "Dễ",
        filename: "may_tinh_cong.py",
        description: "Viết chương trình nhận vào hai số nguyên từ người dùng, tính tổng của chúng và in kết quả ra màn hình.",
        objective: "Luyện tập input(), ép kiểu int(), và toán tử +.",
        sampleCode: `# 1. Nhap du lieu
a_str = input("Nhap so nguyen a: ")
b_str = input("Nhap so nguyen b: ")

# 2. Ep kieu
so_a = int(a_str)
so_b = int(b_str)

# 3. Tinh toan
tong = so_a + so_b

# 4. In ket qua
print("Tong cua hai so la:", tong)`,
        testcases: [
            {
                input: ["10", "25"],
                expectedOutput: "Tong cua hai so la: 35"
            },
            {
                input: ["5", "7"],
                expectedOutput: "Tong cua hai so la: 12"
            },
            {
                input: ["0", "0"],
                expectedOutput: "Tong cua hai so la: 0"
            },
            {
                input: ["100", "200"],
                expectedOutput: "Tong cua hai so la: 300"
            },
            {
                input: ["15", "30"],
                expectedOutput: "Tong cua hai so la: 45"
            },
            {
                input: ["1", "1"],
                expectedOutput: "Tong cua hai so la: 2"
            },
            {
                input: ["50", "25"],
                expectedOutput: "Tong cua hai so la: 75"
            },
            {
                input: ["8", "12"],
                expectedOutput: "Tong cua hai so la: 20"
            },
            {
                input: ["99", "1"],
                expectedOutput: "Tong cua hai so la: 100"
            },
            {
                input: ["123", "456"],
                expectedOutput: "Tong cua hai so la: 579"
            }
        ]
    },
    {
        id: 3,
        title: "Diện Tích Hình Vuông",
        difficulty: "Dễ",
        filename: "dien_tich_hinh_vuong.py",
        description: "Viết chương trình nhận vào độ dài cạnh của một hình vuông (là một số nguyên), tính và in ra diện tích của nó.",
        objective: "Luyện tập input(), int(), và toán tử *.",
        sampleCode: `# 1. Nhap du lieu va ep kieu truc tiep
canh = int(input("Nhap do dai canh hinh vuong: "))

# 2. Tinh toan
dien_tich = canh * canh

# 3. In ket qua
print("Dien tich hinh vuong la:", dien_tich)`,
        testcases: [
            {
                input: ["8"],
                expectedOutput: "Dien tich hinh vuong la: 64"
            },
            {
                input: ["5"],
                expectedOutput: "Dien tich hinh vuong la: 25"
            },
            {
                input: ["1"],
                expectedOutput: "Dien tich hinh vuong la: 1"
            },
            {
                input: ["10"],
                expectedOutput: "Dien tich hinh vuong la: 100"
            },
            {
                input: ["3"],
                expectedOutput: "Dien tich hinh vuong la: 9"
            },
            {
                input: ["7"],
                expectedOutput: "Dien tich hinh vuong la: 49"
            },
            {
                input: ["12"],
                expectedOutput: "Dien tich hinh vuong la: 144"
            },
            {
                input: ["2"],
                expectedOutput: "Dien tich hinh vuong la: 4"
            },
            {
                input: ["15"],
                expectedOutput: "Dien tich hinh vuong la: 225"
            },
            {
                input: ["20"],
                expectedOutput: "Dien tich hinh vuong la: 400"
            }
        ]
    },
    {
        id: 4,
        title: "Diện Tích Hình Tròn",
        difficulty: "Dễ",
        filename: "dien_tich_hinh_tron.py",
        description: "Viết chương trình nhận vào bán kính của một hình tròn (có thể là số thực), tính và in ra diện tích của nó. Sử dụng pi = 3.14159.",
        objective: "Luyện tập input(), ép kiểu float(), toán tử ** và *.",
        sampleCode: `# Khai bao hang so pi
pi = 3.14159

# 1. Nhap du lieu va ep kieu
ban_kinh = float(input("Nhap ban kinh hinh tron: "))

# 2. Tinh toan
dien_tich = pi * (ban_kinh ** 2)

# 3. In ket qua
print("Dien tich hinh tron la:", dien_tich)`,
        testcases: [
            {
                input: ["3"],
                expectedOutput: "Dien tich hinh tron la: 28.27431"
            },
            {
                input: ["5"],
                expectedOutput: "Dien tich hinh tron la: 78.53975"
            },
            {
                input: ["2"],
                expectedOutput: "Dien tich hinh tron la: 12.56636"
            },
            {
                input: ["1"],
                expectedOutput: "Dien tich hinh tron la: 3.14159"
            },
            {
                input: ["4"],
                expectedOutput: "Dien tich hinh tron la: 50.26544"
            },
            {
                input: ["6"],
                expectedOutput: "Dien tich hinh tron la: 113.09724"
            },
            {
                input: ["7"],
                expectedOutput: "Dien tich hinh tron la: 153.93791"
            },
            {
                input: ["10"],
                expectedOutput: "Dien tich hinh tron la: 314.159"
            },
            {
                input: ["2.5"],
                expectedOutput: "Dien tich hinh tron la: 19.634937500000002"
            },
            {
                input: ["8"],
                expectedOutput: "Dien tich hinh tron la: 201.06176"
            }
        ]
    },
    {
        id: 5,
        title: "Định Dạng Ngày Tháng",
        difficulty: "Dễ",
        filename: "dinh_dang_ngay_thang.py",
        description: "Viết chương trình nhận vào ngày, tháng, năm. Sau đó in ra ngày tháng năm theo định dạng ngày/tháng/năm.",
        objective: "Luyện tập input() và hàm print() với tham số sep.",
        sampleCode: `# 1. Nhap du lieu
ngay = input("Nhap ngay: ")
thang = input("Nhap thang: ")
nam = input("Nhap nam: ")

# 2. In ket qua voi dinh dang
print("Ngay thang nam cua ban la: ", end="")
print(ngay, thang, nam, sep="/")`,
        testcases: [
            {
                input: ["15", "8", "2024"],
                expectedOutput: "Ngay thang nam cua ban la: 15/8/2024"
            },
            {
                input: ["1", "1", "2025"],
                expectedOutput: "Ngay thang nam cua ban la: 1/1/2025"
            },
            {
                input: ["25", "12", "2023"],
                expectedOutput: "Ngay thang nam cua ban la: 25/12/2023"
            },
            {
                input: ["3", "5", "2024"],
                expectedOutput: "Ngay thang nam cua ban la: 3/5/2024"
            },
            {
                input: ["10", "10", "2024"],
                expectedOutput: "Ngay thang nam cua ban la: 10/10/2024"
            },
            {
                input: ["7", "2", "2024"],
                expectedOutput: "Ngay thang nam cua ban la: 7/2/2024"
            },
            {
                input: ["20", "11", "2024"],
                expectedOutput: "Ngay thang nam cua ban la: 20/11/2024"
            },
            {
                input: ["31", "12", "2024"],
                expectedOutput: "Ngay thang nam cua ban la: 31/12/2024"
            },
            {
                input: ["14", "2", "2024"],
                expectedOutput: "Ngay thang nam cua ban la: 14/2/2024"
            },
            {
                input: ["9", "9", "2024"],
                expectedOutput: "Ngay thang nam cua ban la: 9/9/2024"
            }
        ]
    },
    {
        id: 6,
        title: "Tính Tổng Tiền Hàng",
        difficulty: "Trung Bình",
        filename: "tinh_tong_tien.py",
        description: "Một cửa hàng bán bút và vở. Viết chương trình nhận vào số lượng bút và số lượng vở khách mua. Biết giá một chiếc bút là 5000 VND, giá một quyển vở là 10000 VND. Tính và in ra tổng số tiền khách phải trả.",
        objective: "Kết hợp nhiều phép tính, biến, input(), int().",
        sampleCode: `# Khai bao gia san pham
gia_but = 5000
gia_vo = 10000

# 1. Nhap du lieu va ep kieu
so_luong_but = int(input("Nhap so luong but: "))
so_luong_vo = int(input("Nhap so luong vo: "))

# 2. Tinh toan
tien_but = so_luong_but * gia_but
tien_vo = so_luong_vo * gia_vo
tong_tien = tien_but + tien_vo

# 3. In ket qua
print("Tong so tien phai tra la:", tong_tien, "VND")`,
        testcases: [
            {
                input: ["3", "5"],
                expectedOutput: "Tong so tien phai tra la: 65000 VND"
            },
            {
                input: ["2", "3"],
                expectedOutput: "Tong so tien phai tra la: 40000 VND"
            },
            {
                input: ["0", "0"],
                expectedOutput: "Tong so tien phai tra la: 0 VND"
            },
            {
                input: ["1", "1"],
                expectedOutput: "Tong so tien phai tra la: 15000 VND"
            },
            {
                input: ["10", "5"],
                expectedOutput: "Tong so tien phai tra la: 100000 VND"
            },
            {
                input: ["5", "0"],
                expectedOutput: "Tong so tien phai tra la: 25000 VND"
            },
            {
                input: ["0", "8"],
                expectedOutput: "Tong so tien phai tra la: 80000 VND"
            },
            {
                input: ["7", "2"],
                expectedOutput: "Tong so tien phai tra la: 55000 VND"
            },
            {
                input: ["4", "6"],
                expectedOutput: "Tong so tien phai tra la: 80000 VND"
            },
            {
                input: ["12", "10"],
                expectedOutput: "Tong so tien phai tra la: 160000 VND"
            }
        ]
    },
    {
        id: 7,
        title: "Đổi Phút Ra Giờ",
        difficulty: "Trung Bình",
        filename: "doi_phut_ra_gio.py",
        description: "Viết chương trình nhận vào một số phút (nguyên), đổi số phút đó ra thành bao nhiêu giờ và bao nhiêu phút.",
        objective: "Luyện tập toán tử chia lấy nguyên // và chia lấy dư %.",
        sampleCode: `# 1. Nhap du lieu va ep kieu
tong_so_phut = int(input("Nhap so phut: "))

# 2. Tinh toan
so_gio = tong_so_phut // 60
so_phut_con_lai = tong_so_phut % 60

# 3. In ket qua
print(str(tong_so_phut) + " phut = " + str(so_gio) + " gio " + str(so_phut_con_lai) + " phut")`,
        testcases: [
            {
                input: ["135"],
                expectedOutput: "135 phut = 2 gio 15 phut"
            },
            {
                input: ["60"],
                expectedOutput: "60 phut = 1 gio 0 phut"
            },
            {
                input: ["90"],
                expectedOutput: "90 phut = 1 gio 30 phut"
            },
            {
                input: ["45"],
                expectedOutput: "45 phut = 0 gio 45 phut"
            },
            {
                input: ["120"],
                expectedOutput: "120 phut = 2 gio 0 phut"
            },
            {
                input: ["180"],
                expectedOutput: "180 phut = 3 gio 0 phut"
            },
            {
                input: ["75"],
                expectedOutput: "75 phut = 1 gio 15 phut"
            },
            {
                input: ["200"],
                expectedOutput: "200 phut = 3 gio 20 phut"
            },
            {
                input: ["30"],
                expectedOutput: "30 phut = 0 gio 30 phut"
            },
            {
                input: ["365"],
                expectedOutput: "365 phut = 6 gio 5 phut"
            }
        ]
    },
    {
        id: 8,
        title: "Tính Tuổi",
        difficulty: "Trung Bình",
        filename: "tinh_tuoi.py",
        description: "Viết chương trình hỏi năm sinh của người dùng, sau đó tính và in ra số tuổi của họ vào năm 2024.",
        objective: "Kết hợp các kiến thức đã học để giải quyết bài toán thực tế.",
        sampleCode: `# Khai bao nam hien tai
nam_hien_tai = 2024

# 1. Nhap du lieu va ep kieu
nam_sinh = int(input("Ban sinh nam nao? "))

# 2. Tinh toan
tuoi = nam_hien_tai - nam_sinh

# 3. In ket qua
print("Vay nam " + str(nam_hien_tai) + " ban " + str(tuoi) + " tuoi.")`,
        testcases: [
            {
                input: ["1999"],
                expectedOutput: "Vay nam 2024 ban 25 tuoi."
            },
            {
                input: ["2000"],
                expectedOutput: "Vay nam 2024 ban 24 tuoi."
            },
            {
                input: ["1995"],
                expectedOutput: "Vay nam 2024 ban 29 tuoi."
            },
            {
                input: ["2005"],
                expectedOutput: "Vay nam 2024 ban 19 tuoi."
            },
            {
                input: ["1990"],
                expectedOutput: "Vay nam 2024 ban 34 tuoi."
            },
            {
                input: ["2010"],
                expectedOutput: "Vay nam 2024 ban 14 tuoi."
            },
            {
                input: ["1985"],
                expectedOutput: "Vay nam 2024 ban 39 tuoi."
            },
            {
                input: ["2015"],
                expectedOutput: "Vay nam 2024 ban 9 tuoi."
            },
            {
                input: ["1980"],
                expectedOutput: "Vay nam 2024 ban 44 tuoi."
            },
            {
                input: ["2020"],
                expectedOutput: "Vay nam 2024 ban 4 tuoi."
            }
        ]
    },
    {
        id: 9,
        title: "Đổi Độ C Sang Độ F",
        difficulty: "Trung Bình",
        filename: "doi_nhiet_do.py",
        description: "Viết chương trình nhận vào nhiệt độ theo độ C (Celsius), đổi nó sang độ F (Fahrenheit) và in kết quả. Công thức: F = (C * 9/5) + 32.",
        objective: "Sử dụng float và thực hiện công thức phức tạp hơn, chú ý thứ tự ưu tiên.",
        sampleCode: `# 1. Nhap du lieu va ep kieu
do_c = float(input("Nhap nhiet do (C): "))

# 2. Tinh toan
do_f = (do_c * 9/5) + 32

# 3. In ket qua
print(f"{do_c} do C = {do_f} do F")`,
        testcases: [
            {
                input: ["37"],
                expectedOutput: "37.0 do C = 98.6 do F"
            },
            {
                input: ["0"],
                expectedOutput: "0.0 do C = 32.0 do F"
            },
            {
                input: ["100"],
                expectedOutput: "100.0 do C = 212.0 do F"
            },
            {
                input: ["25"],
                expectedOutput: "25.0 do C = 77.0 do F"
            },
            {
                input: ["30"],
                expectedOutput: "30.0 do C = 86.0 do F"
            },
            {
                input: ["20"],
                expectedOutput: "20.0 do C = 68.0 do F"
            },
            {
                input: ["15"],
                expectedOutput: "15.0 do C = 59.0 do F"
            },
            {
                input: ["40"],
                expectedOutput: "40.0 do C = 104.0 do F"
            },
            {
                input: ["10"],
                expectedOutput: "10.0 do C = 50.0 do F"
            },
            {
                input: ["35"],
                expectedOutput: "35.0 do C = 95.0 do F"
            }
        ]
    },
    {
        id: 10,
        title: "In Dãy Số Liền Kề",
        difficulty: "Trung Bình",
        filename: "in_day_so.py",
        description: "Viết chương trình nhận vào một số nguyên, sau đó in ra số đó và 2 số nguyên liền sau nó trên cùng một dòng, cách nhau bởi ---.",
        objective: "Luyện tập print() với tham số end và sep.",
        sampleCode: `# 1. Nhap du lieu va ep kieu
so_dau_tien = int(input("Nhap mot so nguyen: "))

# 2. Tinh toan cac so tiep theo
so_thu_hai = so_dau_tien + 1
so_thu_ba = so_dau_tien + 2

# 3. In ket qua
print(f"Day so cua ban la: {so_dau_tien}---{so_thu_hai}---{so_thu_ba}")`,
        testcases: [
            {
                input: ["8"],
                expectedOutput: "Day so cua ban la: 8---9---10"
            },
            {
                input: ["1"],
                expectedOutput: "Day so cua ban la: 1---2---3"
            },
            {
                input: ["0"],
                expectedOutput: "Day so cua ban la: 0---1---2"
            },
            {
                input: ["10"],
                expectedOutput: "Day so cua ban la: 10---11---12"
            },
            {
                input: ["5"],
                expectedOutput: "Day so cua ban la: 5---6---7"
            },
            {
                input: ["15"],
                expectedOutput: "Day so cua ban la: 15---16---17"
            },
            {
                input: ["100"],
                expectedOutput: "Day so cua ban la: 100---101---102"
            },
            {
                input: ["25"],
                expectedOutput: "Day so cua ban la: 25---26---27"
            },
            {
                input: ["50"],
                expectedOutput: "Day so cua ban la: 50---51---52"
            },
            {
                input: ["99"],
                expectedOutput: "Day so cua ban la: 99---100---101"
            }
        ]
    },
    {
        id: 11,
        title: "Đổi Giây Ra Giờ:Phút:Giây",
        difficulty: "Khó",
        filename: "doi_giay.py",
        description: "Viết chương trình nhận vào một tổng số giây (nguyên), đổi nó ra thành dạng Giờ:Phút:Giây và in ra màn hình.",
        objective: "Vận dụng // và % nhiều lần để giải quyết bài toán đa bước.",
        sampleCode: `# 1. Nhap du lieu
tong_giay_input = int(input("Nhap tong so giay: "))

# 2. Tinh toan
# Tinh so gio
so_gio = tong_giay_input // 3600 # 1 gio = 3600 giay

# Tinh so giay con lai sau khi da quy doi ra gio
giay_con_lai_sau_gio = tong_giay_input % 3600

# Tinh so phut tu so giay con lai
so_phut = giay_con_lai_sau_gio // 60

# Tinh so giay con lai cuoi cung
so_giay_cuoi_cung = giay_con_lai_sau_gio % 60

# 3. In ket qua
print(str(tong_giay_input) + " giay tuong ung voi: ", end="")
print(so_gio, so_phut, so_giay_cuoi_cung, sep=":")`,
        testcases: [
            {
                input: ["3661"],
                expectedOutput: "3661 giay tuong ung voi: 1:1:1"
            },
            {
                input: ["3600"],
                expectedOutput: "3600 giay tuong ung voi: 1:0:0"
            },
            {
                input: ["7200"],
                expectedOutput: "7200 giay tuong ung voi: 2:0:0"
            },
            {
                input: ["3665"],
                expectedOutput: "3665 giay tuong ung voi: 1:1:5"
            },
            {
                input: ["60"],
                expectedOutput: "60 giay tuong ung voi: 0:1:0"
            },
            {
                input: ["90"],
                expectedOutput: "90 giay tuong ung voi: 0:1:30"
            },
            {
                input: ["7325"],
                expectedOutput: "7325 giay tuong ung voi: 2:2:5"
            },
            {
                input: ["45"],
                expectedOutput: "45 giay tuong ung voi: 0:0:45"
            },
            {
                input: ["10800"],
                expectedOutput: "10800 giay tuong ung voi: 3:0:0"
            },
            {
                input: ["5555"],
                expectedOutput: "5555 giay tuong ung voi: 1:32:35"
            }
        ]
    },
    {
        id: 12,
        title: "Tính Chỉ Số BMI",
        difficulty: "Khó",
        filename: "tinh_bmi.py",
        description: "Viết lại chương trình tính chỉ số BMI. Chương trình nhận vào cân nặng (kg) và chiều cao (m), sau đó in ra kết quả. Công thức: BMI = Cân nặng / (Chiều cao * Chiều cao).",
        objective: "Vận dụng tổng hợp các kiến thức.",
        sampleCode: `# 1. Nhap du lieu va ep kieu
can_nang = float(input("Nhap can nang cua ban (kg): "))
chieu_cao = float(input("Nhap chieu cao cua ban (m): "))

# 2. Tinh toan
bmi = can_nang / (chieu_cao * chieu_cao)

# 3. In ket qua
print(f"Chi so BMI cua ban la: {bmi}")`,
        testcases: [
            {
                input: ["68.5", "1.75"],
                expectedOutput: "Chi so BMI cua ban la: 22.367346938775512"
            },
            {
                input: ["70", "1.8"],
                expectedOutput: "Chi so BMI cua ban la: 21.604938271604937"
            },
            {
                input: ["60", "1.65"],
                expectedOutput: "Chi so BMI cua ban la: 22.038567493112947"
            },
            {
                input: ["80", "1.75"],
                expectedOutput: "Chi so BMI cua ban la: 26.122448979591837"
            },
            {
                input: ["55", "1.6"],
                expectedOutput: "Chi so BMI cua ban la: 21.484375"
            },
            {
                input: ["75", "1.7"],
                expectedOutput: "Chi so BMI cua ban la: 25.95155709342561"
            },
            {
                input: ["65", "1.68"],
                expectedOutput: "Chi so BMI cua ban la: 23.030045351473923"
            },
            {
                input: ["90", "1.85"],
                expectedOutput: "Chi so BMI cua ban la: 26.296018735362997"
            },
            {
                input: ["50", "1.55"],
                expectedOutput: "Chi so BMI cua ban la: 20.811654526534862"
            },
            {
                input: ["72", "1.72"],
                expectedOutput: "Chi so BMI cua ban la: 24.337748344370862"
            }
        ]
    },
    {
        id: 13,
        title: "Tổng Các Chữ Số",
        difficulty: "Khó",
        filename: "tong_chu_so.py",
        description: "Viết chương trình nhận vào một số nguyên có hai chữ số, tính tổng của hai chữ số đó và in kết quả.",
        objective: "Rèn luyện tư duy logic để tách các chữ số bằng toán tử // và %.",
        sampleCode: `# 1. Nhap du lieu
so_hai_chu_so = int(input("Nhap mot so co hai chu so: "))

# 2. Tinh toan de tach chu so
# Lay chu so hang chuc
chu_so_hang_chuc = so_hai_chu_so // 10

# Lay chu so hang don vi
chu_so_hang_don_vi = so_hai_chu_so % 10

# Tinh tong
tong = chu_so_hang_chuc + chu_so_hang_don_vi

# 3. In ket qua
print("Tong hai chu so la:", tong)`,
        testcases: [
            {
                input: ["75"],
                expectedOutput: "Tong hai chu so la: 12"
            },
            {
                input: ["23"],
                expectedOutput: "Tong hai chu so la: 5"
            },
            {
                input: ["89"],
                expectedOutput: "Tong hai chu so la: 17"
            },
            {
                input: ["45"],
                expectedOutput: "Tong hai chu so la: 9"
            },
            {
                input: ["67"],
                expectedOutput: "Tong hai chu so la: 13"
            },
            {
                input: ["12"],
                expectedOutput: "Tong hai chu so la: 3"
            },
            {
                input: ["99"],
                expectedOutput: "Tong hai chu so la: 18"
            },
            {
                input: ["34"],
                expectedOutput: "Tong hai chu so la: 7"
            },
            {
                input: ["56"],
                expectedOutput: "Tong hai chu so la: 11"
            },
            {
                input: ["78"],
                expectedOutput: "Tong hai chu so la: 15"
            }
        ]
    },
    {
        id: 14,
        title: "Tính Tiền Sau Khi Giảm Giá",
        difficulty: "Khó",
        filename: "tinh_giam_gia.py",
        description: "Viết chương trình nhận vào giá gốc của một sản phẩm và phần trăm giảm giá. Tính và in ra số tiền được giảm và giá cuối cùng phải trả.",
        objective: "Vận dụng tính toán với số thực và logic thực tế.",
        sampleCode: `# 1. Nhap du lieu va ep kieu
gia_goc = float(input("Nhap gia goc san pham: "))
phan_tram_giam = float(input("Nhap phan tram giam gia (%): "))

# 2. Tinh toan
so_tien_giam = gia_goc * (phan_tram_giam / 100)
gia_cuoi_cung = gia_goc - so_tien_giam

# 3. In ket qua
print("So tien duoc giam:", so_tien_giam)
print("Gia cuoi cung phai tra:", gia_cuoi_cung)`,
        testcases: [
            {
                input: ["500000", "15"],
                expectedOutput: "So tien duoc giam: 75000.0\nGia cuoi cung phai tra: 425000.0"
            },
            {
                input: ["100000", "10"],
                expectedOutput: "So tien duoc giam: 10000.0\nGia cuoi cung phai tra: 90000.0"
            },
            {
                input: ["200000", "20"],
                expectedOutput: "So tien duoc giam: 40000.0\nGia cuoi cung phai tra: 160000.0"
            },
            {
                input: ["300000", "25"],
                expectedOutput: "So tien duoc giam: 75000.0\nGia cuoi cung phai tra: 225000.0"
            },
            {
                input: ["150000", "5"],
                expectedOutput: "So tien duoc giam: 7500.0\nGia cuoi cung phai tra: 142500.0"
            },
            {
                input: ["80000", "30"],
                expectedOutput: "So tien duoc giam: 24000.0\nGia cuoi cung phai tra: 56000.0"
            },
            {
                input: ["250000", "12"],
                expectedOutput: "So tien duoc giam: 30000.0\nGia cuoi cung phai tra: 220000.0"
            },
            {
                input: ["400000", "18"],
                expectedOutput: "So tien duoc giam: 72000.0\nGia cuoi cung phai tra: 328000.0"
            },
            {
                input: ["120000", "8"],
                expectedOutput: "So tien duoc giam: 9600.0\nGia cuoi cung phai tra: 110400.0"
            },
            {
                input: ["600000", "22"],
                expectedOutput: "So tien duoc giam: 132000.0\nGia cuoi cung phai tra: 468000.0"
            }
        ]
    }
];

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = codingProblems;
}
