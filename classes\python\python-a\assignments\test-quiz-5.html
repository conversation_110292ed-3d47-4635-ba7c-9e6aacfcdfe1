<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Quiz 5</title>
</head>
<body>
    <h1>Test Quiz 5</h1>
    <div id="content">Loading...</div>
    
    <script type="module">
        async function testLoad() {
            try {
                console.log('Starting to load quiz data...');
                const module = await import('./quiz-data-5.js');
                console.log('Module loaded:', module);
                const quizData = module.default || module.quizData || module;
                console.log('Quiz data:', quizData);
                
                if (quizData && quizData.length > 0) {
                    document.getElementById('content').innerHTML = `
                        <p>Quiz data loaded successfully!</p>
                        <p>Number of questions: ${quizData.length}</p>
                        <p>First question: ${quizData[0].question}</p>
                    `;
                } else {
                    document.getElementById('content').innerHTML = 'Quiz data is empty or invalid';
                }
            } catch (error) {
                console.error('Error loading quiz data:', error);
                document.getElementById('content').innerHTML = `Error: ${error.message}`;
            }
        }
        
        testLoad();
    </script>
</body>
</html>
