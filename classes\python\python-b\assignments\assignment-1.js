// Assignment 1 JavaScript for Python A class
// Import Firebase modules
import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
import { getFirestore, doc, getDoc, updateDoc, setDoc } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
    authDomain: "classroom-web-48bc2.firebaseapp.com",
    projectId: "classroom-web-48bc2",
    storageBucket: "classroom-web-48bc2.firebasestorage.app",
    messagingSenderId: "446746787502",
    appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
    measurementId: "G-742XRP9E96"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// Quiz variables
let quizData = [];
let currentQuestionIndex = 0;
let userAnswers = {};
let timeLeft = 45 * 60; // 45 minutes in seconds
let timerInterval;
let startTime;

// Initialize quiz when page loads
window.addEventListener('DOMContentLoaded', function() {
    // Wait for auth state to be determined
    onAuthStateChanged(auth, (user) => {
        if (user) {
            console.log('User authenticated:', user.email);
            checkAssignmentStatus();
        } else {
            console.log('No user authenticated, redirecting to login');
            alert('Bạn cần đăng nhập để làm bài tập!');
            window.location.href = '../../../auth/';
        }
    });
});

// Global variables for quiz state
let quizStarted = false;
let quizCompleted = false;

// Show confirmation before starting quiz
function showQuizStartConfirmation() {
    return new Promise((resolve) => {
        // Create modal overlay
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        `;

        // Create modal content
        const modal = document.createElement('div');
        modal.style.cssText = `
            background: white;
            padding: 30px;
            border-radius: 15px;
            max-width: 500px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        `;

        modal.innerHTML = `
            <div style="margin-bottom: 20px;">
                <i class="fas fa-clock" style="font-size: 48px; color: #ffc107; margin-bottom: 15px;"></i>
                <h3 style="margin: 0 0 15px 0; color: #333;">Xác nhận bắt đầu làm bài</h3>
                <p style="margin: 0; color: #666; line-height: 1.6;">
                    Bạn chắc chắn vào làm Trắc Nghiệm chứ?<br>
                    Một khi đã vào thì sẽ bắt đầu tính giờ làm bài,<br>
                    hãy lưu ý làm theo sự hướng dẫn của Giáo Viên.
                </p>
            </div>
            <div style="display: flex; gap: 15px; justify-content: center;">
                <button id="cancelQuiz" style="
                    background: #6c757d;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 16px;
                ">Hủy bỏ</button>
                <button id="startQuiz" style="
                    background: #28a745;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 16px;
                ">Bắt đầu làm bài</button>
            </div>
        `;

        overlay.appendChild(modal);
        document.body.appendChild(overlay);

        // Handle button clicks
        document.getElementById('cancelQuiz').onclick = () => {
            document.body.removeChild(overlay);
            resolve(false);
        };

        document.getElementById('startQuiz').onclick = () => {
            document.body.removeChild(overlay);
            resolve(true);
        };
    });
}

// Setup anti-cheat mechanism without page unload warning
function setupAntiCheat() {
    // Track page visibility changes
    document.addEventListener('visibilitychange', function() {
        if (quizStarted && !quizCompleted && document.hidden) {
            // User switched tabs or minimized window during quiz
            console.log('User left page during quiz - marking as cheating attempt');
            handleCheatingAttempt();
        }
    });

    // Track focus changes with delay to avoid false positives from alerts
    window.addEventListener('blur', function() {
        if (quizStarted && !quizCompleted) {
            // Add a small delay to check if quiz is still not completed
            // This prevents false positives when user clicks on alert dialogs
            setTimeout(() => {
                if (quizStarted && !quizCompleted) {
                    console.log('Window lost focus during quiz');
                    handleCheatingAttempt();
                }
            }, 100);
        }
    });
}

// Handle cheating attempt
async function handleCheatingAttempt() {
    if (quizCompleted) return; // Already completed, ignore

    quizCompleted = true; // Mark as completed to prevent multiple saves
    clearInterval(timerInterval); // Stop timer

    // Save 0 score immediately
    await saveZeroScore();

    // Show cheating detection message
    showCheatingDetected();
}

// Save zero score when cheating detected
async function saveZeroScore() {
    const user = auth.currentUser;
    if (!user || isAdmin(user)) return;

    try {
        // Create detailed results for all questions (all marked as incorrect)
        const results = [];
        if (quizData && quizData.length > 0) {
            quizData.forEach((question, index) => {
                results.push({
                    questionIndex: index,
                    question: question.question,
                    options: question.options,
                    userAnswer: -1, // No answer selected
                    correctAnswer: question.correct,
                    isCorrect: false,
                    explanation: question.explanation
                });
            });
        }

        const assignmentData = {
            assignmentId: 'assignment-1-b',
            assignmentTitle: 'Bài Tập 1: Trắc Nghiệm CNTT và Lập Trình',
            score: 0,
            totalQuestions: quizData ? quizData.length : 30,
            completionTime: 0,
            completedAt: new Date().toISOString(),
            results: results,
            cheatingDetected: true,
            reason: 'Left page during quiz'
        };

        await setDoc(doc(db, "users", user.uid, "assignments", "assignment-1"), assignmentData);

        // Update user's assignment count and total score
        const userDoc = await getDoc(doc(db, "users", user.uid));
        if (userDoc.exists()) {
            const userData = userDoc.data();
            await updateDoc(doc(db, "users", user.uid), {
                assignmentCount: (userData.assignmentCount || 0) + 1,
                totalScore: userData.totalScore || 0, // No score added
                lastAssignmentScore: 0,
                lastAssignmentDate: new Date().toISOString(),
                totalCompletionTime: userData.totalCompletionTime || 0
            });
        }

        console.log('Zero score saved due to cheating detection');
    } catch (error) {
        console.error('Error saving zero score:', error);
    }
}

// Show cheating detected message
function showCheatingDetected() {
    // Hide quiz container
    document.getElementById('quizContainer').style.display = 'none';

    // Show results container with cheating message
    document.getElementById('resultsContainer').style.display = 'block';

    // Update page title
    document.querySelector('.assignment-header h1').innerHTML =
        'Bài Tập 1: Trắc Nghiệm CNTT và Lập Trình <span style="color: #dc3545;">(Vi phạm quy định)</span>';

    // Show cheating notice
    const cheatingNotice = document.createElement('div');
    cheatingNotice.style.cssText = `
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 8px;
        text-align: center;
        font-weight: bold;
        border: 2px solid #bd2130;
    `;
    cheatingNotice.innerHTML = `
        <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 10px;"></i>
        <h3>Vi phạm quy định làm bài!</h3>
        <p>Hệ thống đã phát hiện bạn rời khỏi trang trong quá trình làm bài.</p>
        <p><strong>Kết quả: 0 điểm - Bài tập đã được đánh dấu hoàn thành</strong></p>
        <p>Bạn có thể xem đáp án để ôn tập, nhưng không thể làm lại.</p>
        <p style="font-size: 0.9em; margin-top: 15px;">Vui lòng liên hệ giáo viên nếu đây là lỗi hệ thống.</p>
    `;

    // Clear results container and add notice
    const resultsContainer = document.getElementById('resultsContainer');
    resultsContainer.innerHTML = '';
    resultsContainer.appendChild(cheatingNotice);

    // Add view answers button
    const viewAnswersButton = document.createElement('div');
    viewAnswersButton.style.cssText = 'text-align: center; margin: 20px 0;';
    viewAnswersButton.innerHTML = `
        <button onclick="showDetailedResults()" style="
            background: #17a2b8;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            margin-right: 10px;
        ">
            <i class="fas fa-eye"></i> Xem đáp án chi tiết
        </button>
    `;
    resultsContainer.appendChild(viewAnswersButton);

    // Add back button
    const backButton = document.createElement('div');
    backButton.style.cssText = 'text-align: center; margin-top: 10px;';
    backButton.innerHTML = `
        <a href="../python-b.html" style="
            display: inline-block;
            background: #6c757d;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
        ">
            <i class="fas fa-arrow-left"></i> Quay lại lớp học
        </a>
    `;
    resultsContainer.appendChild(backButton);
}

// Show detailed results for cheating case
function showDetailedResults() {
    if (!quizData || quizData.length === 0) {
        alert('Không thể tải dữ liệu câu hỏi. Vui lòng thử lại sau.');
        return;
    }

    const reviewContainer = document.getElementById('reviewContainer');
    let reviewHTML = '<div style="text-align: center; margin-bottom: 20px; padding: 15px; background: #fff3cd; border-radius: 8px; color: #856404; border: 1px solid #ffeaa7;"><strong>📋 Đáp án chi tiết</strong><br>Dưới đây là tất cả câu hỏi và đáp án đúng để bạn ôn tập.</div>';

    quizData.forEach((question, index) => {
        reviewHTML += `
            <div class="review-question">
                <div class="question-number">Câu ${index + 1}: <i class="fas fa-times-circle" style="color: #dc3545;"></i> Không trả lời</div>
                <div class="question-text">${question.question}</div>
                <div class="options">
                    ${question.options.map((option, optIndex) => {
                        let optionClass = '';
                        if (optIndex === question.correct) {
                            optionClass = 'correct-answer';
                        }

                        return `<div class="${optionClass}">
                            ${String.fromCharCode(65 + optIndex)}. ${option}
                            ${optIndex === question.correct ? ' ✓' : ''}
                        </div>`;
                    }).join('')}
                </div>
                <div class="explanation">
                    <strong>Giải thích:</strong> ${question.explanation}
                </div>
            </div>
        `;
    });

    reviewContainer.innerHTML = reviewHTML;

    // Hide the cheating notice and show review
    const cheatingNotice = document.querySelector('#resultsContainer > div');
    if (cheatingNotice) {
        cheatingNotice.style.display = 'none';
    }
}

// Load quiz data
function loadQuizData() {
    quizData = quizQuestions; // Use the global quizQuestions from quiz-data.js
    generateQuestionGrid();
    displayQuestion(0);
}

// Generate question grid indicators
function generateQuestionGrid() {
    const grid = document.getElementById('questionGrid');
    grid.innerHTML = '';
    
    for (let i = 0; i < quizData.length; i++) {
        const indicator = document.createElement('div');
        indicator.className = 'question-indicator';
        indicator.textContent = i + 1;
        indicator.onclick = () => goToQuestion(i);
        grid.appendChild(indicator);
    }
    
    updateQuestionGrid();
}

// Update question grid indicators
function updateQuestionGrid() {
    const indicators = document.querySelectorAll('.question-indicator');
    indicators.forEach((indicator, index) => {
        indicator.classList.remove('current', 'answered');
        
        if (index === currentQuestionIndex) {
            indicator.classList.add('current');
        } else if (userAnswers[index] !== undefined) {
            indicator.classList.add('answered');
        }
    });
}

// Display current question
function displayQuestion(index) {
    const container = document.getElementById('questionsContainer');
    const question = quizData[index];
    
    container.innerHTML = `
        <div class="question active">
            <div class="question-number">Câu ${index + 1}:</div>
            <div class="question-text">${question.question}</div>
            <ul class="options">
                ${question.options.map((option, optIndex) => `
                    <li class="option ${userAnswers[index] === optIndex ? 'selected' : ''}" 
                        onclick="selectAnswer(${index}, ${optIndex})">
                        <input type="radio" name="question${index}" value="${optIndex}" 
                               ${userAnswers[index] === optIndex ? 'checked' : ''}>
                        ${String.fromCharCode(65 + optIndex)}. ${option}
                    </li>
                `).join('')}
            </ul>
        </div>
    `;
    
    updateNavigation();
    updateProgress();
    updateQuestionGrid();
}

// Select answer
function selectAnswer(questionIndex, optionIndex) {
    userAnswers[questionIndex] = optionIndex;
    
    // Update UI
    const options = document.querySelectorAll('.option');
    options.forEach((option, index) => {
        option.classList.remove('selected');
        if (index === optionIndex) {
            option.classList.add('selected');
        }
    });
    
    // Update radio button
    const radio = document.querySelector(`input[name="question${questionIndex}"][value="${optionIndex}"]`);
    if (radio) radio.checked = true;
    
    updateQuestionGrid();
}

// Navigation functions
function nextQuestion() {
    if (currentQuestionIndex < quizData.length - 1) {
        currentQuestionIndex++;
        displayQuestion(currentQuestionIndex);
    }
}

function previousQuestion() {
    if (currentQuestionIndex > 0) {
        currentQuestionIndex--;
        displayQuestion(currentQuestionIndex);
    }
}

function goToQuestion(index) {
    currentQuestionIndex = index;
    displayQuestion(currentQuestionIndex);
}

// Update navigation buttons
function updateNavigation() {
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const questionStatus = document.getElementById('questionStatus');
    
    prevBtn.disabled = currentQuestionIndex === 0;
    nextBtn.style.display = currentQuestionIndex === quizData.length - 1 ? 'none' : 'block';
    
    questionStatus.textContent = `Câu ${currentQuestionIndex + 1} / ${quizData.length}`;
    
    document.getElementById('current-question').textContent = currentQuestionIndex + 1;
    document.getElementById('total-questions').textContent = quizData.length;
}

// Update progress bar
function updateProgress() {
    const answeredCount = Object.keys(userAnswers).length;
    const progress = (answeredCount / quizData.length) * 100;
    document.getElementById('progressFill').style.width = progress + '%';
}

// Timer functions
function startTimer() {
    timerInterval = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timerInterval);
            submitQuiz();
        }
    }, 1000);
}

function updateTimerDisplay() {
    const minutes = Math.floor(timeLeft / 60);
    const seconds = timeLeft % 60;
    document.getElementById('timer').textContent = 
        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

// Submit quiz function
async function submitQuiz() {
    clearInterval(timerInterval);
    quizCompleted = true; // Mark quiz as completed to prevent page unload warning

    const endTime = new Date();
    const completionTime = Math.floor((endTime - startTime) / 1000); // in seconds

    // Calculate score
    let score = 0;
    const results = [];

    quizData.forEach((question, index) => {
        const userAnswer = userAnswers[index];
        const isCorrect = userAnswer !== undefined && userAnswer === question.correct;

        if (isCorrect) score++;

        results.push({
            questionIndex: index,
            question: question.question,
            options: question.options,
            userAnswer: userAnswer !== undefined ? userAnswer : -1, // -1 for no answer
            correctAnswer: question.correct,
            isCorrect: isCorrect,
            explanation: question.explanation
        });
    });

    // Save results to Firebase
    await saveQuizResults(score, completionTime, results);

    // Show results
    showResults(score, completionTime, results);
}

// Check if user is admin
function isAdmin(user) {
    return user && user.email === '<EMAIL>';
}

// Check assignment completion status
async function checkAssignmentStatus() {
    const user = auth.currentUser;
    if (!user) {
        console.error('No user found in checkAssignmentStatus');
        return;
    }

    console.log('Checking assignment status for user:', user.email);
    console.log('User UID:', user.uid);

    try {
        // Add a small delay to ensure auth token is fully loaded
        await new Promise(resolve => setTimeout(resolve, 500));

        // Check if assignment is already completed
        console.log('Attempting to read assignment document...');
        const completedAssignment = await checkCrossClassCompletion(user.uid, 'assignment-1');
        console.log('Assignment document read successful');

        if (completedAssignment && !isAdmin(user)) {
            // Assignment already completed - show results
            console.log('Assignment already completed, showing results');
            showCompletedAssignment(completedAssignment.data, completedAssignment.fromOtherClass);
        } else {
            // Assignment not completed or user is admin - allow taking quiz
            console.log('Assignment not completed or user is admin, initializing quiz');
            initializeQuiz();
        }
    } catch (error) {
        console.error('Error checking assignment status:', error);
        console.error('Error code:', error.code);
        console.error('Error message:', error.message);

        // If error, allow taking quiz anyway
        console.log('Proceeding to initialize quiz despite error');
        initializeQuiz();
    }
}

// Initialize quiz for new attempt
async function initializeQuiz() {
    // Show confirmation dialog before starting
    const confirmed = await showQuizStartConfirmation();
    if (!confirmed) {
        // User cancelled, redirect back to class page
        window.location.href = '../python-b.html';
        return;
    }

    quizStarted = true;
    setupAntiCheat(); // Setup anti-cheat monitoring
    loadQuizData();
    startTimer();
    startTime = new Date();
}

// Function to check if assignment is completed in any class
async function checkCrossClassCompletion(userId, baseAssignmentId) {
    const possibleIds = [
        baseAssignmentId,           // assignment-1 (common)
        `${baseAssignmentId}-c`     // assignment-1-c (Python C specific)
    ];
    
    for (const assignmentId of possibleIds) {
        try {
            const assignmentDoc = await getDoc(doc(db, "users", userId, "assignments", assignmentId));
            if (assignmentDoc.exists()) {
                const fromOtherClass = assignmentId !== baseAssignmentId;
                return {
                    data: assignmentDoc.data(),
                    fromOtherClass: fromOtherClass,
                    originalId: assignmentId
                };
            }
        } catch (error) {
            console.log(`Error checking ${assignmentId}:`, error);
        }
    }
    
    return null;
}

// Show completed assignment results
function showCompletedAssignment(assignmentData, fromOtherClass = false) {
    // Hide quiz container and show results
    document.getElementById('quizContainer').style.display = 'none';
    document.getElementById('resultsContainer').style.display = 'block';

    // Update page title to indicate completed status
    document.querySelector('.assignment-header h1').innerHTML =
        'Bài Tập 1: Trắc Nghiệm CNTT và Lập Trình <span style="color: #28a745;">(Đã hoàn thành)</span>';

    // Add completion notice
    const completionNotice = document.createElement('div');
    completionNotice.style.cssText = `
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 8px;
        text-align: center;
        font-weight: bold;
        border: 2px solid #1e7e34;
    `;
    completionNotice.innerHTML = `
        <i class="fas fa-check-circle"></i>
        <strong>Bài tập đã hoàn thành:</strong> Bạn đã nộp bài này vào ${new Date(assignmentData.completedAt).toLocaleString('vi-VN')}
    `;
    document.getElementById('resultsContainer').insertBefore(completionNotice, document.getElementById('resultsContainer').firstChild);

    // Display the saved results
    displaySavedResults(assignmentData);
}

// Save quiz results to Firebase
async function saveQuizResults(score, completionTime, results) {
    const user = auth.currentUser;
    if (!user) return;

    // Admin can do assignments but results won't be saved
    if (isAdmin(user)) {
        console.log('🔧 Admin mode: Assignment completed but results not saved');
        console.log('Admin score:', score);
        console.log('Admin completion time:', completionTime);
        return; // Exit early for admin
    }

    try {
        // Validate and sanitize all data before saving
        const sanitizedScore = typeof score === 'number' ? score : 0;
        const sanitizedCompletionTime = typeof completionTime === 'number' ? completionTime : 0;
        const sanitizedResults = Array.isArray(results) ? results.map(result => ({
            questionIndex: typeof result.questionIndex === 'number' ? result.questionIndex : 0,
            question: typeof result.question === 'string' ? result.question : 'No question',
            options: Array.isArray(result.options) ? result.options : [],
            userAnswer: typeof result.userAnswer === 'number' ? result.userAnswer : -1,
            correctAnswer: typeof result.correctAnswer === 'number' ? result.correctAnswer : 0,
            isCorrect: typeof result.isCorrect === 'boolean' ? result.isCorrect : false,
            explanation: typeof result.explanation === 'string' ? result.explanation : 'No explanation'
        })) : [];

        const assignmentData = {
            assignmentId: 'assignment-1-b',
            assignmentTitle: 'Bài Tập 1: Trắc Nghiệm CNTT và Lập Trình',
            score: sanitizedScore,
            totalQuestions: quizData && quizData.length ? quizData.length : 0,
            completionTime: sanitizedCompletionTime,
            completedAt: new Date().toISOString(),
            results: sanitizedResults
        };

        // Debug log to check data before saving
        console.log('Saving assignment data:', assignmentData);

        // Double-check if assignment already exists to prevent duplicate scoring
        const existingAssignment = await getDoc(doc(db, "users", user.uid, "assignments", "assignment-1"));
        if (existingAssignment.exists()) {
            console.log('⚠️ Assignment already exists, preventing duplicate save');
            alert('Bài tập này đã được nộp trước đó. Không thể nộp lại.');
            return;
        }

        // Save to user's assignments collection
        await setDoc(doc(db, "users", user.uid, "assignments", "assignment-1"), assignmentData);
        
        // Update user's assignment count and total score
        const userDoc = await getDoc(doc(db, "users", user.uid));
        if (userDoc.exists()) {
            const userData = userDoc.data();
            const currentTotalScore = userData.totalScore || 0;

            // Convert score to 100-point scale (since this lesson only has quiz)
            const scaledScore = Math.round((score / quizData.length) * 100);
            const newTotalScore = currentTotalScore + scaledScore;

            await updateDoc(doc(db, "users", user.uid), {
                assignmentCount: (userData.assignmentCount || 0) + 1,
                totalScore: newTotalScore,
                lastAssignmentScore: scaledScore,
                lastAssignmentDate: new Date().toISOString(),
                totalCompletionTime: (userData.totalCompletionTime || 0) + sanitizedCompletionTime
            });

            console.log(`Updated total score: ${currentTotalScore} + ${scaledScore} = ${newTotalScore} (raw score: ${score}/${quizData.length})`);
        }
        
        console.log('Quiz results saved successfully');

        // Redirect back to class page after a short delay to show results
        setTimeout(() => {
            window.location.href = '../python-b.html';
        }, 3000);
    } catch (error) {
        console.error('Error saving quiz results:', error);
    }
}

// Show results
function showResults(score, completionTime, results) {
    document.getElementById('quizContainer').style.display = 'none';
    document.getElementById('resultsContainer').style.display = 'block';

    // Check if user is admin and show notification
    const user = auth.currentUser;
    if (isAdmin(user)) {
        // Add admin notification
        const adminNotification = document.createElement('div');
        adminNotification.style.cssText = `
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
            border: 2px solid #a93226;
        `;
        adminNotification.innerHTML = `
            <i class="fas fa-user-shield"></i>
            <strong>Chế độ Admin:</strong> Bạn đã hoàn thành bài tập nhưng kết quả không được lưu vào hệ thống.
        `;
        document.getElementById('resultsContainer').insertBefore(adminNotification, document.getElementById('resultsContainer').firstChild);
    } else {
        // Add success notification for regular users
        const successNotice = document.createElement('div');
        successNotice.style.cssText = `
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
            border: 2px solid #1e7e34;
        `;
        successNotice.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <strong>Nộp bài thành công!</strong> Điểm số đã được cập nhật vào bảng xếp hạng.<br>
            <small>Đang chuyển về trang lớp học trong 3 giây...</small>
        `;
        document.getElementById('resultsContainer').insertBefore(successNotice, document.getElementById('resultsContainer').firstChild);
    }

    // Display score
    document.getElementById('finalScore').textContent = score;

    // Display completion time
    const minutes = Math.floor(completionTime / 60);
    const seconds = completionTime % 60;
    document.getElementById('completionTime').textContent =
        `${minutes}:${seconds.toString().padStart(2, '0')}`;

    // Display review
    const reviewContainer = document.getElementById('reviewContainer');
    let reviewHTML = '';

    results.forEach((result, index) => {
        const statusText = result.isCorrect ? 'Đúng' : 'Sai';

        reviewHTML += `
            <div class="review-question">
                <div class="question-number">Câu ${index + 1}: ${statusText}</div>
                <div class="question-text">${result.question}</div>
                <div class="options">
                    ${result.options.map((option, optIndex) => {
                        let optionClass = '';
                        if (optIndex === result.correctAnswer) {
                            optionClass = 'correct-answer';
                        } else if (optIndex === result.userAnswer && !result.isCorrect) {
                            optionClass = 'wrong-answer';
                        }

                        return `<div class="${optionClass}">
                            ${String.fromCharCode(65 + optIndex)}. ${option}
                            ${optIndex === result.correctAnswer ? ' ✓' : ''}
                            ${optIndex === result.userAnswer && !result.isCorrect ? ' ✗' : ''}
                        </div>`;
                    }).join('')}
                </div>
                <div class="explanation">
                    <strong>Giải thích:</strong> ${result.explanation}
                </div>
            </div>
        `;
    });

    reviewContainer.innerHTML = reviewHTML;
}

// Make functions global
window.nextQuestion = nextQuestion;
window.previousQuestion = previousQuestion;
window.goToQuestion = goToQuestion;
window.selectAnswer = selectAnswer;
window.submitQuiz = submitQuiz;

// Display saved results from Firebase
function displaySavedResults(assignmentData) {
    // Display score - convert to 100-point scale for display
    const rawScore = assignmentData.score || 0;
    const totalQuestions = assignmentData.totalQuestions || 30;
    const scaledScore = Math.round((rawScore / totalQuestions) * 100);
    document.getElementById('finalScore').textContent = scaledScore;

    // Display completion time
    const completionTime = assignmentData.completionTime || 0;
    const minutes = Math.floor(completionTime / 60);
    const seconds = completionTime % 60;
    document.getElementById('completionTime').textContent =
        `${minutes}:${seconds.toString().padStart(2, '0')}`;

    // Display review if results exist
    const reviewContainer = document.getElementById('reviewContainer');
    if (assignmentData.results && assignmentData.results.length > 0) {
        let reviewHTML = '';

        assignmentData.results.forEach((result, index) => {
            const statusText = result.isCorrect ? 'Đúng' : 'Sai';

            reviewHTML += `
                <div class="review-question">
                    <div class="question-number">Câu ${index + 1}: ${statusText}</div>
                    <div class="question-text">${result.question}</div>
                    <div class="options">
                        ${result.options.map((option, optIndex) => {
                            let optionClass = '';
                            if (optIndex === result.correctAnswer) {
                                optionClass = 'correct-answer';
                            } else if (optIndex === result.userAnswer && !result.isCorrect) {
                                optionClass = 'wrong-answer';
                            }

                            return `<div class="${optionClass}">
                                ${String.fromCharCode(65 + optIndex)}. ${option}
                                ${optIndex === result.correctAnswer ? ' ✓' : ''}
                                ${optIndex === result.userAnswer && !result.isCorrect ? ' ✗' : ''}
                            </div>`;
                        }).join('')}
                    </div>
                    <div class="explanation">
                        <strong>Giải thích:</strong> ${result.explanation}
                    </div>
                </div>
            `;
        });

        reviewContainer.innerHTML = reviewHTML;
    } else {
        reviewContainer.innerHTML = '<p>Không có dữ liệu chi tiết về bài làm.</p>';
    }
}

// Make functions global
window.nextQuestion = nextQuestion;
window.previousQuestion = previousQuestion;
window.goToQuestion = goToQuestion;
window.selectAnswer = selectAnswer;
window.submitQuiz = submitQuiz;
window.saveQuizResults = saveQuizResults;
window.showDetailedResults = showDetailedResults;

// Note: Authentication check is now handled in DOMContentLoaded event
