// Bộ câu hỏi trắc nghiệm - Bài 3: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> và Chuỗi
const quizData = [
    // MỨC ĐỘ DỄ (Nhận biết & Thông hiểu cơ bản)
    {
        question: "Trong Python, ký hiệu nào được sử dụng cho phép gán giá trị cho một biến?",
        options: ["==", "=", ":=", "<-"],
        correct: 1,
        explanation: "Dấu = được sử dụng để gán giá trị cho biến. Dấu == dùng để so sánh, := là walrus operator (Python 3.8+), <- không phải cú pháp Python."
    },
    {
        question: "Tên biến nào sau đây là hợp lệ trong Python?",
        options: ["1st_number", "my-variable", "_my_var", "class"],
        correct: 2,
        explanation: "_my_var hợp lệ vì bắt đầu bằng dấu gạch dưới. 1st_number không hợp lệ vì bắt đầu bằng số, my-variable chứa dấu gạch ngang, class là từ khóa."
    },
    {
        question: "Kiểu dữ liệu nào dùng để biểu diễn các số nguyên (không có phần thập phân) trong Python?",
        options: ["float", "string", "int", "boolean"],
        correct: 2,
        explanation: "int (integer) là kiểu dữ liệu cho số nguyên. float cho số thực, string cho chuỗi, boolean cho True/False."
    },
    {
        question: "Kiểu dữ liệu nào dùng để biểu diễn các số có phần thập phân trong Python?",
        options: ["integer", "float", "char", "decimal"],
        correct: 1,
        explanation: "float là kiểu dữ liệu cho số thực (có phần thập phân). integer/int cho số nguyên, char không tồn tại trong Python, decimal là module đặc biệt."
    },
    {
        question: "Kiểu dữ liệu nào dùng để biểu diễn một dãy các ký tự (văn bản) trong Python?",
        options: ["text", "char_array", "str", "sequence"],
        correct: 2,
        explanation: "str (string) là kiểu dữ liệu cho chuỗi ký tự. text, char_array, sequence không phải kiểu dữ liệu cơ bản trong Python."
    },
    {
        question: "Để tạo một chuỗi ký tự trong Python, bạn có thể đặt dãy ký tự đó bên trong cặp dấu nào?",
        options: ["Chỉ dấu nháy đơn ' '", "Chỉ dấu nháy kép \" \"", "Cả dấu nháy đơn ' ' và dấu nháy kép \" \"", "Dấu ngoặc vuông [ ]"],
        correct: 2,
        explanation: "Python cho phép tạo chuỗi bằng cả dấu nháy đơn ('') và dấu nháy kép (\"\"). Ngoặc vuông [] dùng cho list."
    },
    {
        question: "Lệnh nào sau đây sẽ gán giá trị số 10 cho biến age?",
        options: ["age == 10", "10 = age", "age = 10", "assign age to 10"],
        correct: 2,
        explanation: "age = 10 là cú pháp đúng để gán. age == 10 là so sánh, 10 = age sai cú pháp, assign age to 10 không phải Python."
    },
    {
        question: "Hàm nào trong Python dùng để kiểm tra kiểu dữ liệu của một biến hoặc giá trị?",
        options: ["checktype()", "typeof()", "datatype()", "type()"],
        correct: 3,
        explanation: "type() là hàm built-in để kiểm tra kiểu dữ liệu. Các hàm khác không tồn tại trong Python."
    },
    {
        question: "Toán tử nào được sử dụng để nối (ghép) hai chuỗi lại với nhau trong Python?",
        options: ["&", "+", "|", "concat()"],
        correct: 1,
        explanation: "Toán tử + dùng để nối chuỗi. & là bitwise AND, | là bitwise OR, concat() không phải toán tử Python."
    },
    {
        question: "Toán tử nào được sử dụng để lặp lại một chuỗi nhiều lần trong Python?",
        options: ["^", "repeat()", "%", "*"],
        correct: 3,
        explanation: "Toán tử * dùng để lặp chuỗi. ^ là XOR, repeat() không phải toán tử, % là modulo."
    },
    {
        question: "Hàm len() trong Python dùng để làm gì khi áp dụng với một chuỗi?",
        options: ["Chuyển chuỗi thành chữ hoa", "Đếm số lượng ký tự trong chuỗi", "Tìm kiếm một ký tự trong chuỗi", "Đảo ngược chuỗi"],
        correct: 1,
        explanation: "len() trả về độ dài (số ký tự) của chuỗi. upper() chuyển chữ hoa, find() tìm kiếm, slicing [::-1] đảo ngược."
    },
    {
        question: "Chỉ số (index) của ký tự đầu tiên trong một chuỗi Python là bao nhiêu?",
        options: ["1", "0", "-1", "Tùy thuộc vào độ dài chuỗi"],
        correct: 1,
        explanation: "Python sử dụng zero-based indexing, ký tự đầu tiên có index 0. Index -1 là ký tự cuối cùng."
    },
    {
        question: "Cho chuỗi s = \"Python\". Biểu thức s[0] sẽ trả về giá trị gì?",
        options: ["\"P\"", "\"p\"", "\"n\"", "Lỗi"],
        correct: 0,
        explanation: "s[0] trả về ký tự đầu tiên là \"P\" (chữ hoa). Python phân biệt chữ hoa/thường."
    },
    {
        question: "Phương thức chuỗi nào dùng để chuyển tất cả các ký tự trong chuỗi thành chữ HOA?",
        options: ["toUpper()", "capitalize()", "upper()", "makeUpper()"],
        correct: 2,
        explanation: "upper() chuyển tất cả ký tự thành chữ hoa. capitalize() chỉ viết hoa chữ cái đầu, các phương thức khác không tồn tại."
    },
    {
        question: "Phương thức chuỗi nào dùng để chuyển tất cả các ký tự trong chuỗi thành chữ thường?",
        options: ["toLower()", "lower()", "smallCaps()", "makeLower()"],
        correct: 1,
        explanation: "lower() chuyển tất cả ký tự thành chữ thường. Các phương thức khác không tồn tại trong Python."
    },

    // MỨC ĐỘ TRUNG BÌNH (Thông hiểu & Vận dụng thấp)
    {
        question: "Tên biến nào sau đây KHÔNG hợp lệ trong Python?",
        options: ["my_age", "AgeOfStudent", "_score", "2ndPlace"],
        correct: 3,
        explanation: "2ndPlace không hợp lệ vì bắt đầu bằng số. Tên biến phải bắt đầu bằng chữ cái hoặc dấu gạch dưới."
    },
    {
        question: "Python có phân biệt chữ hoa và chữ thường trong tên biến không?",
        options: ["Có", "Không", "Chỉ phân biệt với từ khóa", "Tùy thuộc vào cài đặt"],
        correct: 0,
        explanation: "Python phân biệt chữ hoa/thường (case-sensitive). 'age' và 'Age' là hai biến khác nhau."
    },
    {
        question: "Kết quả của type(15.0) là gì?",
        options: ["<class 'int'>", "<class 'float'>", "<class 'number'>", "<class 'str'>"],
        correct: 1,
        explanation: "15.0 có dấu thập phân nên là kiểu float, dù giá trị bằng số nguyên."
    },
    {
        question: "Kết quả của type(\"Hello\") là gì?",
        options: ["<class 'string'>", "<class 'char'>", "<class 'text'>", "<class 'str'>"],
        correct: 3,
        explanation: "Chuỗi trong Python có kiểu str, không phải string hay char."
    },
    {
        question: "Cho a = \"Hello\", b = \"World\". Kết quả của a + \" \" + b là gì?",
        options: ["\"HelloWorld\"", "\"Hello World\"", "\"Hello  World\" (hai khoảng trắng)", "Lỗi"],
        correct: 1,
        explanation: "Nối chuỗi: \"Hello\" + \" \" + \"World\" = \"Hello World\" (một khoảng trắng)."
    },
    {
        question: "Cho s = \"Go\". Kết quả của s * 3 là gì?",
        options: ["\"GoGoGo\"", "\"Go Go Go\"", "\"Go3\"", "Lỗi"],
        correct: 0,
        explanation: "Toán tử * lặp chuỗi: \"Go\" * 3 = \"GoGoGo\" (không có khoảng trắng)."
    },
    {
        question: "Cho text = \"Python is fun\". Kết quả của len(text) là gì?",
        options: ["12", "13", "14 (bao gồm khoảng trắng)", "3 (số lượng từ)"],
        correct: 2,
        explanation: "len() đếm tất cả ký tự bao gồm khoảng trắng: P-y-t-h-o-n-[space]-i-s-[space]-f-u-n = 13 ký tự."
    },
    {
        question: "Cho word = \"Code\". Biểu thức word[-1] sẽ trả về giá trị gì?",
        options: ["\"C\"", "\"e\"", "\"d\"", "Lỗi"],
        correct: 1,
        explanation: "Index âm đếm từ cuối: word[-1] là ký tự cuối cùng \"e\"."
    },
    {
        question: "Cho data = \"Example\". Biểu thức data[1:4] (slicing) sẽ trả về chuỗi con nào?",
        options: ["\"xam\"", "\"xamp\"", "\"Exa\"", "\"amp\""],
        correct: 0,
        explanation: "Slicing [1:4] lấy từ index 1 đến 3 (không bao gồm 4): E[x-a-m]ple = \"xam\"."
    },
    {
        question: "Phương thức chuỗi strip() dùng để làm gì?",
        options: ["Chuyển chuỗi thành danh sách các ký tự", "Loại bỏ các khoảng trắng thừa ở đầu và cuối chuỗi", "Tìm và thay thế một chuỗi con", "Chia chuỗi thành nhiều chuỗi con dựa trên một ký tự phân cách"],
        correct: 1,
        explanation: "strip() loại bỏ whitespace (khoảng trắng, tab, newline) ở đầu và cuối chuỗi."
    },
    {
        question: "Cho message = \" Hello Python! \". Kết quả của message.strip() là gì?",
        options: ["\" Hello Python! \" (Không thay đổi)", "\"Hello Python!\"", "\"HelloPython!\"", "Lỗi"],
        correct: 1,
        explanation: "strip() loại bỏ khoảng trắng đầu và cuối: \" Hello Python! \" → \"Hello Python!\"."
    },
    {
        question: "Phương thức chuỗi find(\"sub\") sẽ trả về giá trị nào nếu không tìm thấy chuỗi con \"sub\" trong chuỗi gốc?",
        options: ["0", "None", "False", "-1"],
        correct: 3,
        explanation: "find() trả về -1 khi không tìm thấy chuỗi con. Trả về index nếu tìm thấy."
    },
    {
        question: "Cho sentence = \"I love Python, Python is great.\". Kết quả của sentence.find(\"Python\") là gì?",
        options: ["7", "19", "0", "-1"],
        correct: 0,
        explanation: "find() trả về index của lần xuất hiện đầu tiên: \"I love Python\" → index 7."
    },
    {
        question: "Phương thức chuỗi replace(\"old\", \"new\") làm gì?",
        options: ["Chỉ thay thế lần xuất hiện đầu tiên của \"old\" bằng \"new\"", "Thay thế tất cả các lần xuất hiện của \"old\" bằng \"new\"", "Xóa tất cả các lần xuất hiện của \"old\"", "Thêm \"new\" vào sau mỗi lần xuất hiện của \"old\""],
        correct: 1,
        explanation: "replace() mặc định thay thế TẤT CẢ các lần xuất hiện của chuỗi cũ bằng chuỗi mới."
    },
    {
        question: "Cho text = \"apple,banana,apple\". Kết quả của text.replace(\"apple\", \"orange\") là gì?",
        options: ["\"orange,banana,apple\"", "\"orange,banana,orange\"", "\"apple,banana,orange\"", "\"orangebananaorange\""],
        correct: 1,
        explanation: "replace() thay thế tất cả: \"apple,banana,apple\" → \"orange,banana,orange\"."
    },

    // MỨC ĐỘ KHÓ (Vận dụng cao & Phân tích, Đánh giá)
    {
        question: "Điều gì sẽ xảy ra nếu bạn cố gắng sử dụng một biến chưa được gán giá trị?",
        options: ["Biến đó sẽ tự động có giá trị là 0", "Biến đó sẽ tự động có giá trị là một chuỗi rỗng \"\"", "Chương trình sẽ bị lỗi (NameError)", "Chương trình sẽ bỏ qua dòng lệnh đó"],
        correct: 2,
        explanation: "Python sẽ raise NameError khi sử dụng biến chưa được định nghĩa. Python không tự động khởi tạo biến."
    },
    {
        question: "Biểu thức my_string = '''Line 1\\nLine 2''' dùng để làm gì?",
        options: ["Gán một chuỗi có chứa lỗi cú pháp cho my_string", "Gán một chuỗi nhiều dòng cho my_string", "Gán một comment nhiều dòng cho my_string", "Tạo một danh sách các chuỗi"],
        correct: 1,
        explanation: "Triple quotes (''') tạo chuỗi nhiều dòng. \\n là ký tự xuống dòng."
    },
    {
        question: "Cho s = \"abcdefgh\". Biểu thức s[::2] sẽ trả về giá trị gì?",
        options: ["\"aceg\"", "\"bdfh\"", "\"abcdefgh\"", "\"hgfedcba\""],
        correct: 0,
        explanation: "s[::2] là slicing với step=2, lấy mỗi ký tự thứ 2: a[b]c[d]e[f]g[h] = \"aceg\"."
    },
    {
        question: "Cho s = \"abcdefgh\". Biểu thức s[::-1] sẽ trả về giá trị gì?",
        options: ["\"aceg\"", "\"bdfh\"", "\"abcdefgh\"", "\"hgfedcba\""],
        correct: 3,
        explanation: "s[::-1] là slicing với step=-1, đảo ngược chuỗi: \"abcdefgh\" → \"hgfedcba\"."
    },
    {
        question: "Nếu x = 10 (kiểu int) và y = \"5\" (kiểu str). Lệnh print(x + y) sẽ cho kết quả gì?",
        options: ["15", "\"105\"", "Lỗi (TypeError)", "105 (kiểu int)"],
        correct: 2,
        explanation: "Python không tự động chuyển đổi kiểu khi cộng int và str, sẽ raise TypeError."
    },
    {
        question: "Cho a = \"123\". Làm thế nào để chuyển biến a thành kiểu số nguyên?",
        options: ["int(a)", "a.to_int()", "convert_to_integer(a)", "Không thể chuyển đổi"],
        correct: 0,
        explanation: "int() là hàm built-in để chuyển đổi sang kiểu số nguyên. Các phương thức khác không tồn tại."
    },
    {
        question: "print(\"She said, \\\"It's sunny!\\\"\") sẽ in ra gì?",
        options: ["She said, \"It's sunny!\"", "She said, It's sunny!", "Lỗi cú pháp", "She said, \\\"It's sunny!\\\""],
        correct: 0,
        explanation: "\\\" là escape sequence cho dấu nháy kép trong chuỗi. Kết quả: She said, \"It's sunny!\""
    },
    {
        question: "Tên biến global = 100 có hợp lệ không? Tại sao?",
        options: ["Hợp lệ, vì nó bắt đầu bằng chữ cái.", "Không hợp lệ, vì \"global\" là một từ khóa của Python.", "Hợp lệ, nhưng không được khuyến khích.", "Không hợp lệ, vì nó chứa ký tự đặc biệt."],
        correct: 1,
        explanation: "\"global\" là từ khóa (keyword) trong Python, không thể dùng làm tên biến."
    },
    {
        question: "Cho s = \"Programming\". Biểu thức s[len(s)-1] tương đương với biểu thức nào sau đây?",
        options: ["s[0]", "s[-1]", "s[10] (nếu len(s) là 11)", "Cả B và C đều đúng"],
        correct: 3,
        explanation: "len(\"Programming\") = 11, s[11-1] = s[10]. s[-1] cũng là ký tự cuối. Cả hai đều đúng."
    },
    {
        question: "Kết quả của print(\"A\" + \"B\" * 2 + \"C\") là gì?",
        options: ["\"ABBC\"", "\"AABBCC\"", "\"ABCABC\"", "Lỗi"],
        correct: 0,
        explanation: "\"B\" * 2 = \"BB\", sau đó nối: \"A\" + \"BB\" + \"C\" = \"ABBC\"."
    },
    {
        question: "Cho my_var = \"  \". Kết quả của len(my_var.strip()) là gì?",
        options: ["2", "0", "1", "Lỗi"],
        correct: 1,
        explanation: "my_var có 2 khoảng trắng, strip() loại bỏ hết → chuỗi rỗng → len = 0."
    },
    {
        question: "Cho text = \"hello\". Lệnh text[0] = \"H\" sẽ làm gì?",
        options: ["Thay đổi chuỗi text thành \"Hello\"", "Gây ra lỗi (TypeError), vì chuỗi trong Python là bất biến (immutable)", "Tạo ra một biến mới tên H", "Không làm gì cả"],
        correct: 1,
        explanation: "Chuỗi trong Python là immutable (bất biến), không thể thay đổi ký tự riêng lẻ."
    },
    {
        question: "Mục đích chính của việc sử dụng các quy tắc đặt tên biến rõ ràng và có ý nghĩa là gì?",
        options: ["Để chương trình chạy nhanh hơn.", "Để làm cho mã nguồn dễ đọc, dễ hiểu và dễ bảo trì hơn.", "Để tiết kiệm bộ nhớ máy tính.", "Để tuân thủ yêu cầu của trình biên dịch."],
        correct: 1,
        explanation: "Tên biến có ý nghĩa giúp code dễ đọc, hiểu và bảo trì. Không ảnh hưởng đến hiệu suất hay bộ nhớ."
    },
    {
        question: "Cho s = \"one two one three one\". Kết quả của s.replace(\"one\", \"1\", 2) là gì? (Tham số thứ ba của replace là số lần thay thế tối đa)",
        options: ["\"1 two 1 three 1\"", "\"1 two 1 three one\"", "\"1 two one three 1\"", "\"12131\""],
        correct: 1,
        explanation: "replace(\"one\", \"1\", 2) chỉ thay thế 2 lần đầu tiên: \"1 two 1 three one\"."
    },
    {
        question: "Biến trong Python có cần khai báo kiểu dữ liệu trước khi sử dụng không?",
        options: ["Có, luôn luôn phải khai báo.", "Không, Python là ngôn ngữ kiểu động, kiểu dữ liệu được xác định tự động khi gán giá trị.", "Chỉ cần khai báo với kiểu số.", "Chỉ cần khai báo với kiểu chuỗi."],
        correct: 1,
        explanation: "Python là dynamically typed, kiểu dữ liệu được xác định tự động khi gán giá trị."
    },
    {
        question: "print(type(str(100))) sẽ cho kết quả gì?",
        options: ["<class 'int'>", "<class 'str'>", "<class 'number'>", "Lỗi"],
        correct: 1,
        explanation: "str(100) chuyển số 100 thành chuỗi \"100\", type() trả về <class 'str'>."
    },
    {
        question: "Cho a = 5, b = 2. Kết quả của print(\"a / b = \" + str(a/b)) là gì?",
        options: ["a / b = 2.5", "Lỗi (không thể nối chuỗi với số)", "\"a / b = 2.5\"", "a / b = 2"],
        correct: 0,
        explanation: "a/b = 2.5, str(2.5) = \"2.5\", nối chuỗi: \"a / b = \" + \"2.5\" = \"a / b = 2.5\"."
    },
    {
        question: "Cho s = \"abc\". Nếu bạn muốn tạo ra chuỗi \"ab!c\", cách nào sau đây là hợp lệ (biết rằng chuỗi là bất biến)?",
        options: ["s[2] = \"!\"", "s_new = s[0:2] + \"!\" + s[2]", "s.insert(2, \"!\")", "s.replace_char_at(2, \"!\")"],
        correct: 1,
        explanation: "Vì chuỗi immutable, phải tạo chuỗi mới: s[0:2] = \"ab\", s[2] = \"c\", nối: \"ab\" + \"!\" + \"c\" = \"ab!c\"."
    },
    {
        question: "Trong các thao tác với chuỗi, \"indexing\" và \"slicing\" khác nhau cơ bản ở điểm nào?",
        options: ["Indexing trả về một ký tự, slicing trả về một chuỗi con (có thể rỗng hoặc chứa nhiều ký tự).", "Indexing dùng ngoặc tròn (), slicing dùng ngoặc vuông [].", "Indexing chỉ dùng số dương, slicing có thể dùng số âm.", "Indexing làm thay đổi chuỗi gốc, slicing tạo ra chuỗi mới."],
        correct: 0,
        explanation: "Indexing (s[0]) trả về 1 ký tự, slicing (s[0:2]) trả về chuỗi con. Cả hai đều dùng [] và có thể dùng số âm."
    },
    {
        question: "Xét đoạn code sau:\nname = \"Alice\"\nage = 30\nname = \"Bob\"\nGiá trị cuối cùng của biến name là gì?",
        options: ["\"Alice\"", "30", "\"Bob\"", "Lỗi, không thể gán lại giá trị cho biến name."],
        correct: 2,
        explanation: "Python cho phép gán lại giá trị cho biến. name cuối cùng = \"Bob\"."
    }
];

// Export for use in quiz system
if (typeof module !== 'undefined' && module.exports) {
    module.exports = quizData;
}

export default quizData;
