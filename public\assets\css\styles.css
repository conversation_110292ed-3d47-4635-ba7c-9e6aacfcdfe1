/* General Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, rgba(255,255,255,0.97) 0%, rgba(255,248,220,0.97) 50%, rgba(255,239,213,0.97) 100%);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Header & Navigation */
header {
    background: linear-gradient(135deg, #FFD700, #FFA500, #FF8C00, #FF6347);
    box-shadow: 0 2px 15px rgba(255, 215, 0, 0.3);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    border-bottom: 2px solid rgba(255, 165, 0, 0.4);
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
}

.logo img {
    height: 50px;
    border-radius: 5px;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 25px;
}

nav ul li a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    padding: 5px 10px;
    transition: all 0.3s ease;
    position: relative;
}

nav ul li a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -2px;
    left: 0;
    background: linear-gradient(to right, #FFD700, #FF8C00);
    transition: width 0.3s ease;
}

nav ul li a:hover::after, nav ul li a.active::after {
    width: 100%;
}

nav ul li a:hover, nav ul li a.active {
    color: #333;
    font-weight: 700;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.5);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    position: relative;
    padding: 80px 20px 40px;
    overflow: hidden;
}

.hero .container {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    width: 100%;
}

/* Starfield Background */
.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(2px 2px at 20px 30px, #fff, transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
        radial-gradient(2px 2px at 160px 30px, #fff, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: sparkle 3s linear infinite;
    opacity: 0.8;
}

/* Shooting Stars */
.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent),
        linear-gradient(90deg, transparent, rgba(255,215,0,0.6), transparent),
        linear-gradient(90deg, transparent, rgba(255,140,0,0.4), transparent);
    background-size: 200px 2px, 150px 1px, 100px 1px;
    background-position: -200px 20%, -150px 40%, -100px 70%;
    background-repeat: no-repeat;
    animation: shootingStars 4s linear infinite;
    pointer-events: none;
}

/* Hero Animations */
@keyframes sparkle {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-100px); }
}

@keyframes shootingStars {
    0% {
        background-position: -200px 20%, -150px 40%, -100px 70%;
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        background-position: 100vw 20%, 100vw 40%, 100vw 70%;
        opacity: 0;
    }
}

@keyframes backgroundMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(60px, 60px); }
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hero-content {
    position: relative;
    z-index: 10;
    max-width: 900px;
    width: 100%;
    padding: 20px;
    margin-bottom: 120px;
    animation: heroFadeIn 1.5s ease-out forwards;
}

@keyframes heroFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.hero h1 {
    font-size: 3.2rem;
    margin-bottom: 40px;
    color: white;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5), 0 0 40px rgba(255, 140, 0, 0.3);
    line-height: 1.3;
    background: linear-gradient(135deg, #ffd700, #ff8c00, #ff6b35);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: textGlow 2s ease-in-out infinite alternate;
    max-width: 100%;
    word-wrap: break-word;
}

@keyframes textGlow {
    from {
        text-shadow: 0 0 20px rgba(255, 215, 0, 0.5), 0 0 40px rgba(255, 140, 0, 0.3);
    }
    to {
        text-shadow: 0 0 30px rgba(255, 215, 0, 0.8), 0 0 60px rgba(255, 140, 0, 0.5);
    }
}

.hero p {
    font-size: 1.6rem;
    margin-bottom: 30px;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    opacity: 1;
    animation: fadeInUp 1s ease-out 0.5s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    animation: fadeInUp 1s ease-out 4s both;
    z-index: 10;
    max-width: 300px;
}

.scroll-text {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.scroll-arrow {
    width: 50px;
    height: 50px;
    border: 2px solid rgba(255, 215, 0, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    background: rgba(255, 215, 0, 0.1);
    backdrop-filter: blur(10px);
    animation: scrollBounce 2s ease-in-out infinite;
    cursor: pointer;
    transition: all 0.3s ease;
}

.scroll-arrow:hover {
    background: rgba(255, 215, 0, 0.2);
    border-color: #ffd700;
    transform: scale(1.1);
}

.scroll-arrow i {
    color: #ffd700;
    font-size: 1.2rem;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

@keyframes scrollBounce {
    0%, 100% {
        transform: translateY(0px);
        box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
    }
    50% {
        transform: translateY(-10px);
        box-shadow: 0 15px 25px rgba(255, 215, 0, 0.4);
    }
}

/* Buttons */
.btn {
    display: inline-block;
    background: linear-gradient(to right, #FFD700, #FF8C00);
    color: white;
    padding: 12px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(255, 140, 0, 0.4);
    position: relative;
    overflow: hidden;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.1));
    transition: left 0.7s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(255, 140, 0, 0.5);
    background: linear-gradient(to right, #FF8C00, #FFD700);
}

/* Footer */
footer {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 25%, #FF8C00 50%, #FF6347 75%, #FF4500 100%);
    padding: 40px 0 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    margin-top: auto;
    color: #333;
    box-shadow: 0 -5px 20px rgba(255, 140, 0, 0.4);
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.8));
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 30px;
    margin-bottom: 20px;
}

.footer-section h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.2rem;
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.5);
}

.footer-logo {
    text-align: left;
}

.footer-logo-container {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.footer-logo-container img {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.footer-logo .logo-text {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.5);
}

.footer-logo .slogan {
    color: #333;
    font-style: italic;
    margin-bottom: 15px;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.3);
}

.footer-contact {
    text-align: left;
}

.footer-contact p {
    margin: 8px 0;
    color: #333;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.3);
}

.footer-contact i {
    color: #333;
    margin-right: 8px;
    width: 16px;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.5);
}

.footer-social {
    text-align: left;
}

.social-links {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.social-link {
    display: inline-flex;
    align-items: center;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 25px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.5);
}

.social-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.25);
}

.social-link.facebook:hover {
    background: #1877f2;
    color: white;
    border-color: #1877f2;
}

.social-link.tiktok:hover {
    background: #000;
    color: white;
    border-color: #000;
}

.social-link.instagram:hover {
    background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
    color: white;
    border-color: transparent;
}

.social-link i {
    margin-right: 6px;
    font-size: 1.1rem;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.2);
    color: #333;
    font-size: 0.9rem;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.3);
}

/* Responsive Footer */
@media (max-width: 768px) {
    /* Hero Section Responsive */
    .typing-text {
        font-size: 2rem;
        line-height: 1.4;
    }

    .scroll-indicator {
        bottom: 25px;
        max-width: 250px;
    }

    .scroll-text {
        font-size: 0.9rem;
        margin-bottom: 8px;
    }

    .scroll-arrow {
        width: 35px;
        height: 35px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 25px;
        text-align: center;
    }

    .footer-section {
        text-align: center !important;
    }

    .social-links {
        justify-content: center;
    }

    footer {
        padding: 30px 0 15px;
    }
}

/* Main content area */
main, section {
    flex: 1;
}

/* Common Elements */
.page-header {
    text-align: center;
    margin: 120px 0 50px;
    animation: fadeIn 0.8s ease-out forwards;
}

.page-header h1 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.page-header h1::after {
    content: '';
    position: absolute;
    width: 50%;
    height: 3px;
    bottom: -10px;
    left: 25%;
    background: linear-gradient(to right, #FFD700, #FF8C00);
    border-radius: 2px;
}

.page-header p {
    color: #666;
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto;
}

/* Card Styles */
.card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Form validation styles */
.form-group input.error,
.form-group select.error,
.form-group textarea.error {
    border-color: #ff4d4d;
    background-color: rgba(255, 77, 77, 0.05);
}

.success-message {
    background-color: rgba(76, 217, 100, 0.1);
    color: #2ca745;
    padding: 15px;
    margin-top: 20px;
    border-radius: 5px;
    font-weight: 500;
    border: 1px solid #2ca745;
    animation: fadeIn 0.5s ease-out forwards;
}

.error-message {
    background-color: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    padding: 15px;
    margin-top: 20px;
    border-radius: 5px;
    font-weight: 500;
    border: 1px solid #e74c3c;
    animation: fadeIn 0.5s ease-out forwards;
}

/* Animation for elements */
.animated {
    animation: fadeInUp 0.8s ease-out forwards;
}

@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Mobile navigation */
.menu-toggle {
    display: none;
    cursor: pointer;
    font-size: 1.5rem;
    color: #333;
}

@media (max-width: 768px) {
    .menu-toggle {
        display: block;
    }
    
    header .container {
        flex-direction: column;
    }
    
    nav {
        width: 100%;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.5s ease;
    }
    
    nav.active {
        max-height: 300px;
    }
    
    nav ul {
        flex-direction: column;
        margin-top: 15px;
    }
    
    nav ul li {
        margin: 5px 0;
    }
    
    .hero {
        min-height: 100vh;
        padding: 100px 15px 40px;
    }

    .hero-content {
        max-width: 100%;
        padding: 15px;
        margin-bottom: 80px;
    }

    .hero h1 {
        font-size: 2.2rem;
        margin-bottom: 15px;
        line-height: 1.3;
    }

    .hero p {
        font-size: 1.3rem;
        margin-bottom: 25px;
    }


}

/* Extra small devices (phones, 480px and down) */
@media (max-width: 480px) {
    .hero {
        padding: 90px 10px 30px;
    }

    .hero h1 {
        font-size: 1.8rem;
        margin-bottom: 12px;
    }

    .hero p {
        font-size: 1.1rem;
        margin-bottom: 20px;
    }

    .hero-content {
        padding: 10px;
        margin-bottom: 60px;
    }


    }

    .contact-details p {
        font-size: 0.9rem;
    }
}

/* Animation Effects */
.floating {
    animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Gradient Text */
.gradient-text {
    background: linear-gradient(to right, #FFD700, #FF8C00);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}



.contact-details h4 {
    font-size: 1.2rem;
    margin-bottom: 5px;
    font-weight: 600;
}

.contact-details p {
    font-size: 1rem;
    margin: 0;
    opacity: 0.9;
}

.facebook-link {
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 15px;
    width: 100%;
}

.facebook-link:hover {
    color: white;
    text-decoration: none;
}

/* ===== NEW HOMEPAGE ANIMATIONS ===== */

/* Typing Animation */
.typing-container {
    margin-bottom: 20px;
}

.typing-text {
    font-size: 2.8rem;
    margin-bottom: 20px;
    color: #333;
    text-shadow: none;
    line-height: 1.3;
    overflow: hidden;
    border-right: 3px solid #FFD700;
    white-space: normal;
    word-wrap: break-word;
    max-width: 100%;
    margin: 0 auto;
    animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from {
        width: 0;
        opacity: 0;
    }
    to {
        width: 100%;
        opacity: 1;
    }
}

@keyframes blink-caret {
    from, to { border-color: transparent }
    50% { border-color: #FFD700; }
}

/* Fade in up animation */
.fade-in-up {
    animation: fadeInUp 1s ease-out 3.5s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin: 50px 0;
    max-width: 1000px;
    animation: fadeInUp 1s ease-out 1s both;
}

.feature-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 30px 20px;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    transition: all 0.4s ease;
    border: 2px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
    border-color: rgba(255, 215, 0, 0.6);
    background: rgba(255, 255, 255, 0.15);
}

.feature-icon {
    font-size: 3.5rem;
    color: #ffd700;
    margin-bottom: 20px;
    animation: iconFloat 3s ease-in-out infinite;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
    position: relative;
    z-index: 2;
}

@keyframes iconFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
    }
    50% {
        transform: translateY(-10px) rotate(5deg);
        text-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
    }
}

.feature-card h3 {
    color: white;
    margin-bottom: 15px;
    font-size: 1.4rem;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}

.feature-card p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    line-height: 1.6;
    position: relative;
    z-index: 2;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* CTA Section */
.cta-section {
    margin-top: 40px;
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 15px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    display: inline-block;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(45deg, #FFD700, #FF8C00);
    color: white;
    border-color: #FFD700;
}

.btn-secondary {
    background: transparent;
    color: #333;
    border-color: #333;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.btn-primary:hover {
    background: linear-gradient(45deg, #FF8C00, #FFD700);
}

.btn-secondary:hover {
    background: #333;
    color: white;
}

/* Pulse Animation */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 215, 0, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0);
    }
}

/* Floating Elements */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
}

.floating-element {
    position: absolute;
    font-size: 2rem;
    opacity: 0.6;
    animation: float var(--duration) ease-in-out infinite;
    animation-delay: var(--delay);
}

.floating-element:nth-child(1) { top: 20%; left: 10%; }
.floating-element:nth-child(2) { top: 60%; left: 85%; }
.floating-element:nth-child(3) { top: 30%; left: 80%; }
.floating-element:nth-child(4) { top: 70%; left: 15%; }
.floating-element:nth-child(5) { top: 40%; left: 50%; }

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

/* Responsive Design for New Elements */
@media (max-width: 768px) {
    .typing-text {
        font-size: 2.2rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        margin: 30px 0;
    }

    .feature-card {
        padding: 25px 15px;
    }

    .cta-section {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
        text-align: center;
    }

    .floating-element {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .typing-text {
        font-size: 1.8rem;
    }

    .feature-icon {
        font-size: 2.5rem;
    }

    .floating-element {
        font-size: 1.2rem;
    }
}

/* Code Camp Event Section */
.code-camp-event {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    padding: 100px 0;
    position: relative;
    overflow: hidden;
    color: white;
}

.code-camp-event::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,215,0,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.event-header {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
    z-index: 2;
}

.event-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #ff6b6b, #ff8e53);
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 20px;
    animation: pulse 2s infinite;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.event-title {
    font-size: 3.5rem;
    margin-bottom: 20px;
    position: relative;
}

.gradient-text {
    background: linear-gradient(135deg, #FFD700, #FFA500, #FF8C00);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    text-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
}

.title-decoration {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-top: 15px;
}

.decoration-line {
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #FFD700, #FF8C00);
    border-radius: 2px;
}

.title-decoration i {
    color: #FFD700;
    font-size: 1.5rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.event-subtitle {
    font-size: 1.3rem;
    color: #b8c6db;
    margin-top: 15px;
}

.event-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    margin-bottom: 60px;
    position: relative;
    z-index: 2;
}

.info-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.info-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.info-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.info-header i {
    color: #FFD700;
    font-size: 1.3rem;
}

.info-header h3 {
    color: white;
    font-size: 1.2rem;
    margin: 0;
}

.date-range {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
}

.date-item {
    text-align: center;
    flex: 1;
}

.date-label {
    display: block;
    font-size: 0.9rem;
    color: #b8c6db;
    margin-bottom: 5px;
}

.date-value {
    display: block;
    font-size: 1.3rem;
    font-weight: 700;
    color: #FFD700;
}

.date-separator {
    color: #FFD700;
    font-size: 1.2rem;
}

.prizes-section {
    position: relative;
    z-index: 2;
}

.prizes-title {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 40px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.prizes-title i {
    color: #FFD700;
    font-size: 1.8rem;
}

.prizes-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.prize-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 30px 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
}

.prize-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, transparent 0%, rgba(255, 215, 0, 0.1) 50%, transparent 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.prize-card:hover::before {
    opacity: 1;
}

.prize-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

.prize-card.top1 {
    border-color: #FFD700;
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
}

.prize-card.top2 {
    border-color: #C0C0C0;
    box-shadow: 0 0 30px rgba(192, 192, 192, 0.3);
}

.prize-card.top3 {
    border-color: #CD7F32;
    box-shadow: 0 0 30px rgba(205, 127, 50, 0.3);
}

.prize-rank {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #FFD700, #FF8C00);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-weight: 800;
    color: white;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
}

.rank-number {
    font-size: 1.5rem;
    line-height: 1;
}

.rank-text {
    font-size: 0.7rem;
    line-height: 1;
}

.prize-badge {
    position: relative;
    margin: 30px 0 20px;
}

.badge-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    animation: float 3s ease-in-out infinite;
    filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.5));
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.badge-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { opacity: 0.5; transform: translate(-50%, -50%) scale(1); }
    to { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
}

.prize-details h4 {
    color: white;
    font-size: 1.1rem;
    margin-bottom: 10px;
}

.prize-value {
    font-size: 1.8rem;
    font-weight: 800;
    color: #FFD700;
    margin-bottom: 5px;
}

.prize-details p {
    color: #b8c6db;
    font-size: 0.9rem;
}

.event-cta {
    text-align: center;
    position: relative;
    z-index: 2;
}

.btn-event {
    background: linear-gradient(135deg, #FFD700, #FF8C00);
    color: white;
    padding: 15px 35px;
    border-radius: 50px;
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 10px;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

.btn-event:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.6);
    background: linear-gradient(135deg, #FF8C00, #FFD700);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 13px 33px;
    border-radius: 50px;
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 10px;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(255, 255, 255, 0.2);
}

/* Animated Background Elements */
.event-bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.bg-element {
    position: absolute;
    font-size: 2rem;
    opacity: 0.1;
    animation: floatBg 6s ease-in-out infinite;
    animation-delay: var(--delay);
}

.bg-element.trophy { top: 10%; left: 10%; }
.bg-element.code { top: 20%; right: 15%; }
.bg-element.fire { bottom: 30%; left: 20%; }
.bg-element.star { top: 60%; right: 10%; }
.bg-element.rocket { bottom: 10%; right: 25%; }

@keyframes floatBg {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(5deg); }
    66% { transform: translateY(10px) rotate(-3deg); }
}

/* Responsive Design for Code Camp Event */
@media (max-width: 1024px) {
    .event-content {
        gap: 40px;
    }

    .prizes-grid {
        gap: 20px;
    }

    .event-title {
        font-size: 3rem;
    }
}

@media (max-width: 768px) {
    .code-camp-event {
        padding: 60px 0;
    }

    .event-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .prizes-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .event-title {
        font-size: 2.5rem;
    }

    .date-range {
        flex-direction: column;
        gap: 15px;
    }

    .date-separator {
        transform: rotate(90deg);
    }

    .btn-event, .btn-secondary {
        display: block;
        margin: 10px auto;
        width: fit-content;
    }
}

@media (max-width: 480px) {
    .event-title {
        font-size: 2rem;
    }

    .prizes-title {
        font-size: 1.5rem;
    }

    .prize-card {
        padding: 25px 15px;
    }

    .badge-image {
        width: 60px;
        height: 60px;
    }

    .prize-rank {
        width: 50px;
        height: 50px;
    }

    .rank-number {
        font-size: 1.2rem;
    }
}

/* Courses Section */
.courses-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%);
    position: relative;
    overflow: hidden;
    color: white;
}

.courses-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 30%, rgba(114, 9, 183, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(15, 52, 96, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(83, 52, 131, 0.1) 0%, transparent 50%);
    animation: backgroundMove 30s linear infinite;
    z-index: 1;
}

.courses-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.05) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
    background-size: 80px 80px, 50px 50px;
    animation: backgroundMove 25s linear infinite reverse;
    opacity: 0.6;
    z-index: 1;
}

.courses-header {
    text-align: center;
    margin-bottom: 60px;
}

.courses-title {
    font-size: 2.8rem;
    margin-bottom: 20px;
    color: white;
    position: relative;
    display: inline-block;
}

.courses-title .gradient-text {
    background: linear-gradient(135deg, #ffd700, #ff8c00, #ff6b35);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.courses-subtitle {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin: 0 auto;
}

.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.course-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.4s ease;
    position: relative;
    border: 2px solid transparent;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.course-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Python Course Styling */
.python-course {
    background: linear-gradient(135deg, #3776ab 0%, #ffd43b 100%);
    color: white;
}

.python-course:hover {
    border-color: #3776ab;
}

.python-course .course-icon i {
    color: #ffd43b;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Scratch Course Styling */
.scratch-course {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: white;
}

.scratch-course:hover {
    border-color: #ff6b35;
}

.scratch-course .course-icon i {
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* STEM Course Styling */
.stem-course {
    background: linear-gradient(135deg, #6f42c1 0%, #007bff 100%);
    color: white;
}

.stem-course:hover {
    border-color: #6f42c1;
}

.stem-course .course-icon i {
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.course-header {
    padding: 25px 25px 0;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.course-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.course-icon i {
    font-size: 2.5rem;
}

.course-badge {
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.course-content {
    padding: 20px 25px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.course-title {
    font-size: 1.4rem;
    margin-bottom: 20px;
    font-weight: 600;
    line-height: 1.3;
    min-height: 3.6rem;
    display: flex;
    align-items: center;
}

.course-details {
    margin-bottom: 20px;
    flex: 1;
}

.detail-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-size: 0.95rem;
}

.detail-item i {
    width: 20px;
    margin-right: 10px;
    opacity: 0.9;
}

.detail-item.special {
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 8px;
    margin: 15px 0;
}

.trial-offer {
    background: rgba(255, 255, 255, 0.15);
    padding: 10px 15px;
    border-radius: 10px;
    margin-top: 15px;
    display: flex;
    align-items: center;
    font-weight: 500;
}

.trial-offer i {
    margin-right: 8px;
    color: #ffd700;
}

.course-features {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 15px;
}

.feature-tag {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 0.85rem;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.course-footer {
    padding: 0 25px 25px;
    margin-top: auto;
}

.btn-course {
    width: 100%;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    border: none;
    padding: 12px 20px;
    border-radius: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.btn-course:hover {
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Background Elements */
.courses-bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.courses-bg-elements .bg-element {
    position: absolute;
    font-size: 2rem;
    opacity: 0.15;
    animation: float 6s ease-in-out infinite;
    color: rgba(255, 255, 255, 0.3);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
}

.courses-bg-elements .book {
    top: 10%;
    left: 5%;
    animation-delay: var(--delay);
}

.courses-bg-elements .graduation {
    top: 20%;
    right: 10%;
    animation-delay: var(--delay);
}

.courses-bg-elements .lightbulb {
    bottom: 30%;
    left: 8%;
    animation-delay: var(--delay);
}

.courses-bg-elements .computer {
    bottom: 15%;
    right: 15%;
    animation-delay: var(--delay);
}

.courses-bg-elements .science {
    top: 50%;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: var(--delay);
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .courses-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 0 15px;
    }

    .courses-title {
        font-size: 2.2rem;
    }

    .courses-subtitle {
        font-size: 1.1rem;
    }

    .course-card {
        margin: 0 10px;
    }

    .course-icon {
        width: 60px;
        height: 60px;
    }

    .course-icon i {
        font-size: 2rem;
    }
}

/* Events Overview Section */
.events-overview {
    padding: 80px 0;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
    position: relative;
    overflow: hidden;
    color: white;
}

.events-overview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(83, 52, 131, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(15, 52, 96, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(22, 33, 62, 0.05) 0%, transparent 50%);
    opacity: 0.6;
    z-index: 1;
    pointer-events: none;
}

.events-header {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
    z-index: 2;
}

.events-title {
    font-size: 2.8rem;
    margin-bottom: 20px;
    position: relative;
    display: inline-block;
}

.events-title .gradient-text {
    background: linear-gradient(135deg, #FFD700, #FF8C00, #FFA500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.title-decoration {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-top: 15px;
}

.decoration-line {
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #FFD700, #FF8C00);
    border-radius: 2px;
}

.title-decoration i {
    color: #FFD700;
    font-size: 1.5rem;
    animation: bounce 2s infinite;
}

.events-subtitle {
    font-size: 1.3rem;
    color: #ccc;
    max-width: 600px;
    margin: 0 auto;
}

.events-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.event-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transition: all 0.4s ease;
    position: relative;
    border: 2px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    height: 320px;
    color: white;
}

.event-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

/* Theme-specific card styling */
.summer-card {
    background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 25%, #ffa726 50%, #ffcc02 75%, #ffeb3b 100%);
    border: 2px solid rgba(255, 107, 107, 0.5);
}

.summer-card:hover {
    border-color: #ff6b6b;
    box-shadow: 0 20px 40px rgba(255, 107, 107, 0.3);
}

.farm-card {
    background: linear-gradient(135deg, #2d5016 0%, #4a7c59 50%, #6b8e23 100%);
    border: 2px solid rgba(50, 205, 50, 0.3);
}

.farm-card:hover {
    border-color: #32cd32;
    box-shadow: 0 20px 40px rgba(50, 205, 50, 0.3);
}

.tet-card {
    background: linear-gradient(135deg, #8b0000 0%, #dc143c 25%, #b8860b 50%, #cd853f 75%, #d2691e 100%);
    border: 2px solid rgba(255, 215, 0, 0.5);
}

.tet-card:hover {
    border-color: #ffd700;
    box-shadow: 0 20px 40px rgba(255, 215, 0, 0.3);
}

.love-card {
    background: linear-gradient(135deg, #ff69b4 0%, #ff1493 25%, #dc143c 50%, #b22222 75%, #8b0000 100%);
    border: 2px solid rgba(255, 182, 193, 0.5);
}

.love-card:hover {
    border-color: #ff69b4;
    box-shadow: 0 20px 40px rgba(255, 105, 180, 0.3);
}

.event-card-header {
    padding: 20px 20px 0;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.event-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.event-badge.active {
    background: linear-gradient(135deg, #ff6b6b, #ff8e53);
    color: white;
    animation: pulse 2s infinite;
}

.event-badge.upcoming {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    color: white;
}

.event-icon {
    font-size: 2rem;
    animation: float 3s ease-in-out infinite;
}

.event-card-content {
    padding: 15px 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.event-card-title {
    font-size: 1.3rem;
    margin-bottom: 8px;
    font-weight: 600;
    color: white;
    line-height: 1.3;
}

.event-card-subtitle {
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 15px;
    line-height: 1.4;
}

.event-card-info {
    margin-bottom: 15px;
}

.info-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
}

.info-item i {
    width: 16px;
    margin-right: 8px;
    color: rgba(255, 255, 255, 0.7);
}

.event-card-footer {
    padding: 0 20px 20px;
    margin-top: auto;
}

.btn-event-card {
    width: 100%;
    background: linear-gradient(135deg, #FFD700, #FF8C00);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 10px;
    font-weight: 600;
    font-size: 0.9rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-event-card:hover {
    background: linear-gradient(135deg, #FF8C00, #FFD700);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
    color: white;
    text-decoration: none;
}

.events-cta {
    text-align: center;
    margin-top: 50px;
    position: relative;
    z-index: 2;
}

.events-cta .btn {
    background: linear-gradient(135deg, #FFD700, #FF8C00);
    color: white;
    padding: 15px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

.events-cta .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.6);
    background: linear-gradient(135deg, #FF8C00, #FFD700);
    color: white;
    text-decoration: none;
}

/* Animated Background Elements */
.events-bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.events-bg-elements .bg-element {
    position: absolute;
    font-size: 2rem;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
    color: #FFD700;
}

.events-bg-elements .trophy { top: 10%; left: 10%; animation-delay: 0s; }
.events-bg-elements .code { top: 20%; right: 15%; animation-delay: 1s; }
.events-bg-elements .fire { bottom: 30%; left: 20%; animation-delay: 2s; }
.events-bg-elements .star { top: 60%; right: 10%; animation-delay: 3s; }
.events-bg-elements .rocket { bottom: 10%; right: 25%; animation-delay: 4s; }
.events-bg-elements .heart { top: 40%; left: 50%; animation-delay: 5s; }

/* Animation Keyframes */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .events-overview {
        padding: 60px 0;
    }

    .events-title {
        font-size: 2.2rem;
    }

    .events-subtitle {
        font-size: 1.1rem;
    }

    .events-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 0 15px;
    }

    .event-card {
        height: auto;
        min-height: 280px;
    }

    .event-card-title {
        font-size: 1.2rem;
    }

    .event-card-subtitle {
        font-size: 0.9rem;
    }

    .info-item {
        font-size: 0.85rem;
    }

    .btn-event-card {
        font-size: 0.85rem;
        padding: 8px 12px;
    }
}

@media (max-width: 480px) {
    .events-overview {
        padding: 40px 0;
    }

    .events-title {
        font-size: 1.8rem;
    }

    .events-subtitle {
        font-size: 1rem;
    }

    .events-grid {
        gap: 15px;
        padding: 0 10px;
    }

    .event-card {
        min-height: 260px;
    }

    .event-card-header {
        padding: 15px 15px 0;
    }

    .event-card-content {
        padding: 10px 15px;
    }

    .event-card-footer {
        padding: 0 15px 15px;
    }

    .event-icon {
        font-size: 1.5rem;
    }

    .event-badge {
        font-size: 0.7rem;
        padding: 4px 8px;
    }

    .decoration-line {
        width: 40px;
    }

    .title-decoration {
        gap: 15px;
    }

    .title-decoration i {
        font-size: 1.2rem;
    }
}

/* VTA Sci-Fi UI Elements - Adapted from test.css */
/* Variables and Mixins */
:root {
    --vta-gold: #FFD700;
    --vta-orange: #FF8C00;
    --vta-yellow: #FFA500;
    --vta-dark: #1a1a1a;
    --vta-light: #ffffff;
    --vta-accent: #f39c12;
    --vta-easing: cubic-bezier(0.21, 0.61, 0.35, 1);
    --vta-easing-bounce: cubic-bezier(0, 1.99, 0.6, 1.01);
}

/* VTA UI Border Elements */
.vta-ui-border-v2.t {
    width: 620px;
    height: 30px;
    top: -5px;
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
    fill: var(--vta-gold);
}

.vta-ui-border-v2.t .batt {
    width: 12px;
    height: 3px;
    position: absolute;
    left: 50%;
    bottom: -1px;
    transform: translate(-50%, 0);
    background: var(--vta-orange);
    z-index: 1;
    box-shadow: 0 0 10px var(--vta-orange);
}

.vta-ui-c-border {
    position: absolute;
    width: 50vw;
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    transition: all 0.3s ease-out;
}

.vta-ui-c-border svg {
    fill: var(--vta-gold);
    opacity: 0.3;
    width: 100%;
}

.vta-ui-c-border .batt {
    width: 12px;
    height: 3px;
    background: var(--vta-orange);
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
    bottom: -1px;
}

.vta-ui-c-border.t {
    top: -10px;
    transform: scale(1,1) translate3d(0, -100%, 0);
}

.vta-ui-c-border.b {
    bottom: -10px;
    transform: scale(-1, -1) translate3d(0, -100%, 0);
}

/* VTA UI Border Sides */
.vta-ui-border {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
    height: 80vh;
    z-index: 1;
}

.vta-ui-border:before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 1px;
    background: rgba(255, 215, 0, 0.3);
}

.vta-ui-border .cap {
    width: 4px;
    height: 4px;
    border-width: 4px;
    border-style: solid;
    left: -5px;
    position: absolute;
}

.vta-ui-border .cap.t {
    top: -2vh;
    border-color: var(--vta-orange) transparent transparent transparent;
}

.vta-ui-border .cap.b {
    bottom: -2vh;
    border-color: transparent transparent var(--vta-orange) transparent;
}

.vta-ui-border .batt {
    display: block;
    width: 3px;
    height: 12px;
    background: var(--vta-orange);
}

.vta-ui-border.l {
    left: 3vw;
}

.vta-ui-border.r {
    right: 3vw;
}

/* VTA UI Cross Elements */
.vta-ui-cross {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    transition: all 0.6s ease-out;
    opacity: 0.5;
}

.vta-ui-cross .border-x {
    position: absolute;
    top: 10%;
    bottom: 10%;
    left: 50%;
    width: 1px;
    background: rgba(255, 255, 255, 0.15);
}

.vta-ui-cross .border-x.f {
    transform: rotate(110deg);
}

.vta-ui-cross .border-x.l {
    transform: rotate(-110deg);
}

.vta-ui-cross .border-horz {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: rgba(255, 255, 255, 0.15);
}

.vta-ui-cross .border-vert {
    position: absolute;
    top: 10vh;
    bottom: 10vh;
    left: 50%;
    width: 1px;
    background: rgba(255, 255, 255, 0.15);
}

.vta-ui-cross .dot {
    border-radius: 100%;
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 255, 0.5);
    position: absolute;
    left: -2px;
}

.vta-ui-cross .dot.t {
    top: 0;
}

.vta-ui-cross .dot.b {
    bottom: 0;
}

.vta-ui-cross .x-hair {
    width: 30px;
    height: 30px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.vta-ui-cross .line {
    width: 1px;
    height: 40%;
    background: rgba(255, 255, 255, 0.15);
    position: absolute;
}

.vta-ui-cross .line:nth-of-type(1) {
    top: 0;
    left: 0;
    transform-origin: 0 0;
    transform: rotate(-45deg);
}

.vta-ui-cross .line:nth-of-type(2) {
    top: 0;
    right: 0;
    transform-origin: 100% 0;
    transform: rotate(45deg);
}

.vta-ui-cross .line:nth-of-type(3) {
    bottom: 0;
    left: 0;
    transform-origin: 0% 100%;
    transform: rotate(45deg);
}

.vta-ui-cross .line:nth-of-type(4) {
    bottom: 0;
    right: 0;
    transform-origin: 100% 100%;
    transform: rotate(-45deg);
}

/* VTA UI Circles */
.vta-ui-circles {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.vta-ui-circles.l {
    left: 5vw;
}

.vta-ui-circles.r {
    right: 5vw;
}

.vta-ui-circles .circle {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    border: 1px solid rgba(255, 215, 0, 0.4);
    background: rgba(255, 140, 0, 0.1);
}

/* VTA UI Text Elements */
.vta-ui-text {
    position: absolute;
    top: 20%;
    color: var(--vta-gold);
    font-family: 'Orbitron', monospace;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.vta-ui-text.l {
    left: 8vw;
}

.vta-ui-text.r {
    right: 8vw;
}

.vta-ui-text h5 {
    font-size: 0.9rem;
    margin: 0 0 10px 0;
    opacity: 0.8;
}

.vta-ui-text .t-o {
    display: flex;
    gap: 5px;
}

.vta-ui-text svg {
    width: 20px;
    height: 20px;
    fill: rgba(255, 215, 0, 0.3);
}

/* VTA Header Section */
.vta-header {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.vta-header .wrap {
    position: relative;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* VTA UI Inner Borders */
.vta-ui-inner-border {
    position: absolute;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--vta-gold), transparent);
    opacity: 0.6;
}

.vta-ui-inner-border.t {
    top: 0;
}

.vta-ui-inner-border.b {
    bottom: 0;
}

.vta-ui-inner-border button {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: 1px solid var(--vta-gold);
    color: var(--vta-gold);
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.vta-ui-inner-border button:hover {
    background: var(--vta-gold);
    color: var(--vta-dark);
}

.vta-ui-inner-border .border {
    display: block;
    width: 20px;
    height: 1px;
    background: var(--vta-gold);
    margin-right: 8px;
}

.vta-ui-inner-border .icon-arrow {
    width: 12px;
    height: 12px;
    fill: currentColor;
}

/* VTA UI Tics */
.vta-ui-tics {
    position: absolute;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    padding: 0 50px;
}

.vta-ui-tics.t {
    top: 10px;
}

.vta-ui-tics.b {
    bottom: 10px;
}

.vta-ui-tics .tic {
    width: 1px;
    height: 8px;
    background: rgba(255, 215, 0, 0.3);
    display: block;
}

/* VTA UI Grid and Box */
.vta-ui-grid {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
}

.vta-ui-box {
    position: relative;
    padding: 40px;
    border: 1px solid rgba(255, 215, 0, 0.3);
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.vta-ui-box .title {
    font-size: 3rem;
    color: var(--vta-gold);
    text-align: center;
    margin: 0;
    font-family: 'Orbitron', monospace;
    text-transform: uppercase;
    letter-spacing: 3px;
}

.vta-ui-box .title em {
    color: var(--vta-orange);
    font-style: normal;
}

/* VTA UI FX Elements */
.vta-ui-fx {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.vta-ui-fx .ui-inner-border {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(180deg, transparent, var(--vta-gold), transparent);
}

.vta-ui-fx .ui-inner-border.l {
    left: 0;
}

.vta-ui-fx .ui-inner-border.r {
    right: 0;
}

.vta-ui-fx .ui-corner {
    position: absolute;
    width: 12px;
    height: 12px;
    border: 2px solid var(--vta-gold);
}

.vta-ui-fx .ui-corner:nth-child(3) {
    top: 0;
    left: 0;
    border-right: none;
    border-bottom: none;
}

.vta-ui-fx .ui-corner:nth-child(4) {
    top: 0;
    right: 0;
    border-left: none;
    border-bottom: none;
}

.vta-ui-fx .ui-corner:nth-child(5) {
    bottom: 0;
    left: 0;
    border-right: none;
    border-top: none;
}

.vta-ui-fx .ui-corner:nth-child(6) {
    bottom: 0;
    right: 0;
    border-left: none;
    border-top: none;
}

/* VTA UI Batteries */
.vta-ui-batts {
    position: absolute;
    display: flex;
    gap: 4px;
}

.vta-ui-batts.l {
    left: -20px;
    top: 50%;
    transform: translateY(-50%);
    flex-direction: column;
}

.vta-ui-batts.r {
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    flex-direction: column;
}

.vta-ui-batts.t {
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    flex-direction: row;
}

.vta-ui-batts.b {
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    flex-direction: row;
}

.vta-ui-batts .batt {
    width: 8px;
    height: 8px;
    background: var(--vta-orange);
    border-radius: 1px;
    box-shadow: 0 0 6px var(--vta-orange);
}

/* Responsive Design for VTA UI */
@media (max-width: 768px) {
    .vta-ui-border {
        height: 60vh;
    }

    .vta-ui-border.l {
        left: 2vw;
    }

    .vta-ui-border.r {
        right: 2vw;
    }

    .vta-ui-circles.l {
        left: 3vw;
    }

    .vta-ui-circles.r {
        right: 3vw;
    }

    .vta-ui-text.l {
        left: 5vw;
    }

    .vta-ui-text.r {
        right: 5vw;
    }

    .vta-ui-box .title {
        font-size: 2rem;
    }
}