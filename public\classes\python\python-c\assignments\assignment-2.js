// Assignment 2 JavaScript for Python C class
// Import Firebase modules
import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
import { getFirestore, doc, getDoc, updateDoc, setDoc } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
    authDomain: "classroom-web-48bc2.firebaseapp.com",
    projectId: "classroom-web-48bc2",
    storageBucket: "classroom-web-48bc2.firebasestorage.app",
    messagingSenderId: "446746787502",
    appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
    measurementId: "G-742XRP9E96"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// Quiz variables
let quizData = [];
let currentQuestionIndex = 0;
let userAnswers = {};
let timeLeft = 30 * 60; // 30 minutes in seconds
let timerInterval;
let startTime;

// Initialize quiz when page loads
window.addEventListener('DOMContentLoaded', function() {
    // Setup page unload warning
    setupPageUnloadWarning();

    // Wait for auth state to be determined
    onAuthStateChanged(auth, (user) => {
        if (user) {
            console.log('User authenticated:', user.email);
            checkAssignmentStatus();
        } else {
            console.log('No user authenticated, redirecting to login');
            alert('Bạn cần đăng nhập để làm bài tập!');
            window.location.href = '../../../auth/';
        }
    });
});

// Setup page unload warning
function setupPageUnloadWarning() {
    let quizStarted = false;
    let quizCompleted = false;

    // Monitor quiz start through timer
    const checkQuizStart = () => {
        if (timerInterval && !quizStarted) {
            quizStarted = true;
        }
    };

    // Check every second if quiz has started
    setInterval(checkQuizStart, 1000);

    // Add beforeunload event listener
    window.addEventListener('beforeunload', function(e) {
        if (quizStarted && !quizCompleted) {
            const message = 'Việc tải lại hoặc rời khỏi trang sẽ ghi nhận điểm 0. Bạn có chắc chắn muốn tiếp tục?';
            e.preventDefault();
            e.returnValue = message;
            return message;
        }
    });

    // Mark quiz as completed when results are shown
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
                const resultsContainer = document.getElementById('resultsContainer');
                if (resultsContainer && resultsContainer.style.display === 'block') {
                    quizCompleted = true;
                }
            }
        });
    });

    // Start observing
    setTimeout(() => {
        const resultsContainer = document.getElementById('resultsContainer');
        if (resultsContainer) {
            observer.observe(resultsContainer, { attributes: true, attributeFilter: ['style'] });
        }
    }, 1000);
}

// Load quiz data
function loadQuizData() {
    quizData = quizQuestions2; // Use the global quizQuestions2 from quiz-data-2.js
    generateQuestionGrid();
    displayQuestion(0);
}

// Generate question grid indicators
function generateQuestionGrid() {
    const grid = document.getElementById('questionGrid');
    grid.innerHTML = '';
    
    for (let i = 0; i < quizData.length; i++) {
        const indicator = document.createElement('div');
        indicator.className = 'question-indicator';
        indicator.textContent = i + 1;
        indicator.onclick = () => goToQuestion(i);
        grid.appendChild(indicator);
    }
    
    updateQuestionGrid();
}

// Update question grid indicators
function updateQuestionGrid() {
    const indicators = document.querySelectorAll('.question-indicator');
    indicators.forEach((indicator, index) => {
        indicator.classList.remove('current', 'answered');
        
        if (index === currentQuestionIndex) {
            indicator.classList.add('current');
        } else if (userAnswers[index] !== undefined) {
            indicator.classList.add('answered');
        }
    });
}

// Display current question
function displayQuestion(index) {
    const container = document.getElementById('questionsContainer');
    const question = quizData[index];
    
    container.innerHTML = `
        <div class="question active">
            <div class="question-number">Câu ${index + 1}:</div>
            <div class="question-text">${question.question}</div>
            <ul class="options">
                ${question.options.map((option, optIndex) => `
                    <li class="option ${userAnswers[index] === optIndex ? 'selected' : ''}" 
                        onclick="selectAnswer(${index}, ${optIndex})">
                        <input type="radio" name="question${index}" value="${optIndex}" 
                               ${userAnswers[index] === optIndex ? 'checked' : ''}>
                        ${String.fromCharCode(65 + optIndex)}. ${option}
                    </li>
                `).join('')}
            </ul>
        </div>
    `;
    
    updateNavigation();
    updateProgress();
    updateQuestionGrid();
}

// Select answer
function selectAnswer(questionIndex, optionIndex) {
    userAnswers[questionIndex] = optionIndex;
    
    // Update UI
    const options = document.querySelectorAll('.option');
    options.forEach((option, index) => {
        option.classList.remove('selected');
        if (index === optionIndex) {
            option.classList.add('selected');
        }
    });
    
    // Update radio button
    const radio = document.querySelector(`input[name="question${questionIndex}"][value="${optionIndex}"]`);
    if (radio) radio.checked = true;
    
    updateQuestionGrid();
}

// Navigation functions
function nextQuestion() {
    if (currentQuestionIndex < quizData.length - 1) {
        currentQuestionIndex++;
        displayQuestion(currentQuestionIndex);
    }
}

function previousQuestion() {
    if (currentQuestionIndex > 0) {
        currentQuestionIndex--;
        displayQuestion(currentQuestionIndex);
    }
}

function goToQuestion(index) {
    currentQuestionIndex = index;
    displayQuestion(currentQuestionIndex);
}

// Update navigation buttons
function updateNavigation() {
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const questionStatus = document.getElementById('questionStatus');
    
    prevBtn.disabled = currentQuestionIndex === 0;
    nextBtn.style.display = currentQuestionIndex === quizData.length - 1 ? 'none' : 'block';
    
    questionStatus.textContent = `Câu ${currentQuestionIndex + 1} / ${quizData.length}`;
    
    document.getElementById('current-question').textContent = currentQuestionIndex + 1;
    document.getElementById('total-questions').textContent = quizData.length;
}

// Update progress bar
function updateProgress() {
    const answeredCount = Object.keys(userAnswers).length;
    const progress = (answeredCount / quizData.length) * 100;
    document.getElementById('progressFill').style.width = progress + '%';
}

// Timer functions
function startTimer() {
    timerInterval = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timerInterval);
            submitQuiz();
        }
    }, 1000);
}

function updateTimerDisplay() {
    const minutes = Math.floor(timeLeft / 60);
    const seconds = timeLeft % 60;
    document.getElementById('timer').textContent = 
        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

// Submit quiz function
async function submitQuiz() {
    clearInterval(timerInterval);
    
    const endTime = new Date();
    const completionTime = Math.floor((endTime - startTime) / 1000); // in seconds
    
    // Calculate score
    let correctAnswers = 0;
    const results = [];

    quizData.forEach((question, index) => {
        const userAnswer = userAnswers[index];
        const isCorrect = userAnswer !== undefined && userAnswer === question.correct;

        if (isCorrect) correctAnswers++;

        results.push({
            questionIndex: index,
            question: question.question,
            options: question.options,
            userAnswer: userAnswer !== undefined ? userAnswer : -1, // -1 for no answer
            correctAnswer: question.correct,
            isCorrect: isCorrect,
            explanation: question.explanation
        });
    });

    // Calculate final score out of 50 points (quiz part of lesson 2)
    const finalScore = Math.round((correctAnswers / quizData.length) * 50);

    // Save results to Firebase
    await saveQuizResults(finalScore, completionTime, results, correctAnswers);
    
    // Show results
    showResults(finalScore, completionTime, results, correctAnswers);
}

// Check if user is admin
function isAdmin(user) {
    return user && user.email === '<EMAIL>';
}

// Check assignment completion status
async function checkAssignmentStatus() {
    const user = auth.currentUser;
    if (!user) {
        console.error('No user found in checkAssignmentStatus');
        return;
    }

    console.log('Checking assignment 2-C status for user:', user.email);
    console.log('User UID:', user.uid);

    try {
        // Add a small delay to ensure auth token is fully loaded
        await new Promise(resolve => setTimeout(resolve, 500));

        // Check if assignment is already completed
        console.log('Attempting to read assignment 2-C document...');
        const completedAssignment = await checkCrossClassCompletion(user.uid, 'assignment-2');
        console.log('Assignment 2-C document read successful');

        if (completedAssignment && !isAdmin(user)) {
            // Assignment already completed - show results
            console.log('Assignment 2-C already completed, showing results');
            showCompletedAssignment(completedAssignment.data, completedAssignment.fromOtherClass);
        } else {
            // Assignment not completed or user is admin - allow taking quiz
            console.log('Assignment 2-C not completed or user is admin, initializing quiz');
            initializeQuiz();
        }
    } catch (error) {
        console.error('Error checking assignment 2-C status:', error);
        console.error('Error code:', error.code);
        console.error('Error message:', error.message);

        // If error, allow taking quiz anyway
        console.log('Proceeding to initialize quiz despite error');
        initializeQuiz();
    }
}

// Initialize quiz for new attempt
function initializeQuiz() {
    loadQuizData();
    startTimer();
    startTime = new Date();
}

// Function to check if assignment is completed in any class (Python C version)
async function checkCrossClassCompletion(userId, baseAssignmentId) {
    const possibleIds = [
        `${baseAssignmentId}-c`,    // assignment-2-c (Python C specific) - check this first
        baseAssignmentId            // assignment-2 (common)
    ];
    
    for (const assignmentId of possibleIds) {
        try {
            const assignmentDoc = await getDoc(doc(db, "users", userId, "assignments", assignmentId));
            if (assignmentDoc.exists()) {
                const fromOtherClass = assignmentId !== `${baseAssignmentId}-c`;
                return {
                    data: assignmentDoc.data(),
                    fromOtherClass: fromOtherClass,
                    originalId: assignmentId
                };
            }
        } catch (error) {
            console.log(`Error checking ${assignmentId}:`, error);
        }
    }
    
    return null;
}

// Show completed assignment results
function showCompletedAssignment(assignmentData, fromOtherClass = false) {
    // Hide quiz container and show results
    document.getElementById('quizContainer').style.display = 'none';
    document.getElementById('resultsContainer').style.display = 'block';

    // Update page title to indicate completed status
    const titleSuffix = fromOtherClass ? '(Đã hoàn thành ở lớp khác)' : '(Đã hoàn thành)';
    const titleColor = fromOtherClass ? '#ffc107' : '#28a745';
    document.querySelector('.assignment-header h1').innerHTML =
        `Bài Tập 2: Python và Cài Đặt Môi Trường <span style="color: ${titleColor};">${titleSuffix}</span>`;

    // Add completion notice
    const completionNotice = document.createElement('div');
    const noticeColor = fromOtherClass ? 'linear-gradient(135deg, #ffc107, #fd7e14)' : 'linear-gradient(135deg, #28a745, #20c997)';
    const borderColor = fromOtherClass ? '#e0a800' : '#1e7e34';
    completionNotice.style.cssText = `
        background: ${noticeColor};
        color: white;
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 8px;
        text-align: center;
        font-weight: bold;
        border: 2px solid ${borderColor};
    `;

    const noticeText = fromOtherClass
        ? `<i class="fas fa-info-circle"></i>
           <strong>Đã hoàn thành ở lớp khác:</strong> Bạn đã nộp bài này vào ${new Date(assignmentData.timestamp?.toDate() || assignmentData.completedAt).toLocaleString('vi-VN')}.<br>
           <small>Bạn có thể xem lại đáp án để ôn tập nhưng không thể làm lại bài tập.</small>`
        : `<i class="fas fa-check-circle"></i>
           <strong>Bài tập đã hoàn thành:</strong> Bạn đã nộp bài này vào ${new Date(assignmentData.timestamp?.toDate() || assignmentData.completedAt).toLocaleString('vi-VN')}`;

    completionNotice.innerHTML = noticeText;
    document.getElementById('resultsContainer').insertBefore(completionNotice, document.getElementById('resultsContainer').firstChild);

    // Display the saved results
    displaySavedResults(assignmentData);
}

// Save quiz results to Firebase
async function saveQuizResults(score, completionTime, results, correctAnswers) {
    const user = auth.currentUser;
    if (!user) return;

    // Admin can do assignments but results won't be saved
    if (isAdmin(user)) {
        console.log('🔧 Admin mode: Assignment 2-C completed but results not saved');
        console.log('Admin score:', score);
        console.log('Admin correct answers:', correctAnswers);
        console.log('Admin completion time:', completionTime);
        return; // Exit early for admin
    }

    try {
        // Validate and sanitize all data before saving
        const sanitizedScore = typeof score === 'number' ? score : 0;
        const sanitizedCompletionTime = typeof completionTime === 'number' ? completionTime : 0;
        const sanitizedResults = Array.isArray(results) ? results.map(result => ({
            questionIndex: typeof result.questionIndex === 'number' ? result.questionIndex : 0,
            question: typeof result.question === 'string' ? result.question : 'No question',
            options: Array.isArray(result.options) ? result.options : [],
            userAnswer: typeof result.userAnswer === 'number' ? result.userAnswer : -1,
            correctAnswer: typeof result.correctAnswer === 'number' ? result.correctAnswer : 0,
            isCorrect: typeof result.isCorrect === 'boolean' ? result.isCorrect : false,
            explanation: typeof result.explanation === 'string' ? result.explanation : 'No explanation'
        })) : [];

        const assignmentData = {
            assignmentId: 'assignment-2-c',
            assignmentTitle: 'Bài Tập 2: Python và Cài Đặt Môi Trường - Python C',
            score: sanitizedScore,
            correctAnswers: typeof correctAnswers === 'number' ? correctAnswers : 0,
            totalQuestions: quizData && quizData.length ? quizData.length : 0,
            completionTime: sanitizedCompletionTime,
            completedAt: new Date().toISOString(),
            results: sanitizedResults
        };

        // Debug log to check data before saving
        console.log('Saving assignment 2-C data:', assignmentData);

        // Save to user's assignments collection
        await setDoc(doc(db, "users", user.uid, "assignments", "assignment-2-c"), assignmentData);

        // Update user's assignment count and total score
        const userDoc = await getDoc(doc(db, "users", user.uid));
        if (userDoc.exists()) {
            const userData = userDoc.data();
            const currentTotalScore = userData.totalScore || 0;
            const newTotalScore = currentTotalScore + score;

            await updateDoc(doc(db, "users", user.uid), {
                assignmentCount: (userData.assignmentCount || 0) + 1,
                totalScore: newTotalScore,
                lastAssignmentScore: score,
                lastAssignmentDate: new Date().toISOString(),
                totalCompletionTime: (userData.totalCompletionTime || 0) + sanitizedCompletionTime
            });

            console.log(`Updated total score: ${currentTotalScore} + ${score} = ${newTotalScore}`);
        }

        console.log('Quiz 2-C results saved successfully');
        alert('✅ Đã lưu kết quả bài tập thành công! Điểm số đã được cập nhật vào bảng xếp hạng.');
    } catch (error) {
        console.error('Error saving quiz 2-C results:', error);
    }
}

// Show results
function showResults(score, completionTime, results, correctAnswers) {
    document.getElementById('quizContainer').style.display = 'none';
    document.getElementById('resultsContainer').style.display = 'block';

    // Check if user is admin and show notification
    const user = auth.currentUser;
    if (isAdmin(user)) {
        // Add admin notification
        const adminNotification = document.createElement('div');
        adminNotification.style.cssText = `
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
            border: 2px solid #a93226;
        `;
        adminNotification.innerHTML = `
            <i class="fas fa-user-shield"></i>
            <strong>Chế độ Admin:</strong> Bạn đã hoàn thành bài tập nhưng kết quả không được lưu vào hệ thống.
        `;
        document.getElementById('resultsContainer').insertBefore(adminNotification, document.getElementById('resultsContainer').firstChild);
    }

    // Display score with breakdown
    document.getElementById('finalScore').textContent = `${score}/50 (${correctAnswers}/${results.length} câu đúng)`;

    // Display completion time
    const minutes = Math.floor(completionTime / 60);
    const seconds = completionTime % 60;
    document.getElementById('completionTime').textContent =
        `${minutes}:${seconds.toString().padStart(2, '0')}`;

    // Display review
    const reviewContainer = document.getElementById('reviewContainer');
    let reviewHTML = '';

    results.forEach((result, index) => {
        const statusText = result.isCorrect ? 'Đúng' : 'Sai';

        reviewHTML += `
            <div class="review-question">
                <div class="question-number">Câu ${index + 1}: ${statusText}</div>
                <div class="question-text">${result.question}</div>
                <div class="options">
                    ${result.options.map((option, optIndex) => {
                        let optionClass = '';
                        if (optIndex === result.correctAnswer) {
                            optionClass = 'correct-answer';
                        } else if (optIndex === result.userAnswer && !result.isCorrect) {
                            optionClass = 'wrong-answer';
                        }

                        return `<div class="${optionClass}">
                            ${String.fromCharCode(65 + optIndex)}. ${option}
                            ${optIndex === result.correctAnswer ? ' ✓' : ''}
                            ${optIndex === result.userAnswer && !result.isCorrect ? ' ✗' : ''}
                        </div>`;
                    }).join('')}
                </div>
                <div class="explanation">
                    <strong>Giải thích:</strong> ${result.explanation}
                </div>
            </div>
        `;
    });

    reviewContainer.innerHTML = reviewHTML;
}

// Display saved results from Firebase
function displaySavedResults(assignmentData) {
    // Display score with breakdown
    const score = assignmentData.score || 0;
    const correctAnswers = assignmentData.correctAnswers || 0;
    const totalQuestions = assignmentData.totalQuestions || 0;
    document.getElementById('finalScore').textContent = `${score}/50 (${correctAnswers}/${totalQuestions} câu đúng)`;

    // Display completion time
    const completionTime = assignmentData.completionTime || 0;
    const minutes = Math.floor(completionTime / 60);
    const seconds = completionTime % 60;
    document.getElementById('completionTime').textContent =
        `${minutes}:${seconds.toString().padStart(2, '0')}`;

    // Display review if results exist
    const reviewContainer = document.getElementById('reviewContainer');
    if (assignmentData.results && assignmentData.results.length > 0) {
        let reviewHTML = '';

        assignmentData.results.forEach((result, index) => {
            const statusText = result.isCorrect ? 'Đúng' : 'Sai';

            reviewHTML += `
                <div class="review-question">
                    <div class="question-number">Câu ${index + 1}: ${statusText}</div>
                    <div class="question-text">${result.question}</div>
                    <div class="options">
                        ${result.options.map((option, optIndex) => {
                            let optionClass = '';
                            if (optIndex === result.correctAnswer) {
                                optionClass = 'correct-answer';
                            } else if (optIndex === result.userAnswer && !result.isCorrect) {
                                optionClass = 'wrong-answer';
                            }

                            return `<div class="${optionClass}">
                                ${String.fromCharCode(65 + optIndex)}. ${option}
                                ${optIndex === result.correctAnswer ? ' ✓' : ''}
                                ${optIndex === result.userAnswer && !result.isCorrect ? ' ✗' : ''}
                            </div>`;
                        }).join('')}
                    </div>
                    <div class="explanation">
                        <strong>Giải thích:</strong> ${result.explanation}
                    </div>
                </div>
            `;
        });

        reviewContainer.innerHTML = reviewHTML;
    } else {
        reviewContainer.innerHTML = '<p>Không có dữ liệu chi tiết về bài làm.</p>';
    }
}

// Make functions global
window.nextQuestion = nextQuestion;
window.previousQuestion = previousQuestion;
window.goToQuestion = goToQuestion;
window.selectAnswer = selectAnswer;
window.submitQuiz = submitQuiz;
