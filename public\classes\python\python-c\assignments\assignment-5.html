<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bài Tập 5: <PERSON> v<PERSON> - <PERSON> C</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../../../../assets/images/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../../../../assets/images/favicon.png">
    <link rel="apple-touch-icon" href="../../../../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="../../../../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="114x114" href="../../../../assets/images/favicon.png">

    <link rel="stylesheet" href="../../../../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* Dark Background with Starfield Effect */
        body {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* Shooting stars animation */
        .shooting-star {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #FFD700;
            border-radius: 50%;
            box-shadow: 0 0 10px #FFD700;
            animation: shoot 3s linear infinite;
        }

        @keyframes shoot {
            0% {
                transform: translateX(-100px) translateY(100px);
                opacity: 1;
            }
            100% {
                transform: translateX(100vw) translateY(-100vh);
                opacity: 0;
            }
        }

        /* Floating particles */
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 215, 0, 0.6);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .assignment-container {
            max-width: 1000px;
            width: 100%;
            margin: 120px auto 50px;
            padding: 0 20px;
            position: relative;
            z-index: 1;
            box-sizing: border-box; /* Đảm bảo padding không làm tăng kích thước */
        }

        .assignment-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            color: white;
            border-radius: 15px;
        }

        .assignment-header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #FFD700;
        }

        .timer-info {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .timer-display {
            font-size: 1.5rem;
            font-weight: bold;
            color: #FFD700;
        }

        .quiz-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            position: relative;
            min-height: 400px; /* Cố định chiều cao tối thiểu */
            max-height: 600px; /* Cố định chiều cao tối đa */
            display: flex;
            flex-direction: column;
        }

        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255, 215, 0, 0.3);
        }

        .question-number {
            font-size: 1.2rem;
            font-weight: bold;
            color: #FFD700;
        }

        .question-text {
            font-size: 1.1rem;
            margin-bottom: 25px;
            line-height: 1.6;
            color: white;
            flex-grow: 1;
            overflow-y: auto;
            max-height: 150px; /* Giới hạn chiều cao câu hỏi */
        }

        .options-container {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-bottom: 20px;
            overflow-y: auto;
            max-height: 200px; /* Giới hạn chiều cao phần lựa chọn */
        }

        .option {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
            font-size: 1rem;
            line-height: 1.4;
        }

        .option:hover {
            background: rgba(255, 215, 0, 0.1);
            border-color: #FFD700;
            transform: translateX(5px);
        }

        .option.selected {
            background: rgba(255, 215, 0, 0.2);
            border-color: #FFD700;
            color: #FFD700;
        }

        .option.correct {
            background: rgba(40, 167, 69, 0.3);
            border-color: #28a745;
            color: #28a745;
        }

        .option.incorrect {
            background: rgba(220, 53, 69, 0.3);
            border-color: #dc3545;
            color: #dc3545;
        }

        .explanation {
            background: rgba(255, 215, 0, 0.1);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            color: white;
            line-height: 1.5;
            display: none;
        }

        .explanation.show {
            display: block;
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: auto;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 215, 0, 0.3);
        }

        .nav-btn {
            background: linear-gradient(135deg, #FFD700, #FF8C00);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }

        .nav-btn:disabled {
            background: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.5);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .submit-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            display: none;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }

        .results-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            text-align: center;
            display: none;
        }

        .score-display {
            font-size: 3rem;
            font-weight: bold;
            color: #FFD700;
            margin-bottom: 20px;
        }

        .score-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .score-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 20px;
        }

        .score-item h3 {
            color: #FFD700;
            margin-bottom: 10px;
        }

        .score-item p {
            font-size: 1.5rem;
            font-weight: bold;
            margin: 0;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #FFD700;
            text-decoration: none;
            font-weight: 500;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            color: #FFA500;
            transform: translateX(-5px);
        }

        /* Progress bar */
        .progress-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            height: 8px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-bar {
            background: linear-gradient(135deg, #FFD700, #FF8C00);
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
        }

        /* Anti-cheat warning */
        .warning-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .warning-content {
            background: rgba(220, 53, 69, 0.9);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            max-width: 500px;
            margin: 20px;
        }

        .warning-content h2 {
            color: #FFD700;
            margin-bottom: 15px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .assignment-container {
                margin: 100px auto 30px;
                padding: 0 15px;
            }

            .assignment-header h1 {
                font-size: 1.5rem;
            }

            .timer-info {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .quiz-container {
                padding: 20px;
                min-height: 350px;
                max-height: 500px;
            }

            .question-text {
                font-size: 1rem;
                max-height: 120px;
            }

            .options-container {
                max-height: 150px;
            }

            .option {
                padding: 12px 15px;
                font-size: 0.9rem;
            }

            .navigation-buttons {
                flex-direction: column;
                gap: 15px;
            }

            .nav-btn {
                width: 100%;
                justify-content: center;
            }

            .score-breakdown {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../../../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../../../../index.html">Trang Chủ</a></li>
                    <li><a href="../../../">Lớp Học</a></li>
                    <li><a href="../../../../achievements/">Thành Tích</a></li>
                    <li><a href="../../../../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../../../../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../../../../research/">Sự kiện</a></li>
                    <li><a href="../../../../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Background Effects -->
    <div id="backgroundEffects"></div>

    <!-- Warning Overlay -->
    <div class="warning-overlay" id="warningOverlay">
        <div class="warning-content">
            <h2><i class="fas fa-exclamation-triangle"></i> Cảnh Báo!</h2>
            <p>Bạn đã chuyển tab hoặc cửa sổ khác. Điều này có thể được coi là gian lận.</p>
            <p><strong>Số lần vi phạm: <span id="violationCount">0</span></strong></p>
            <p>Nếu vi phạm quá 3 lần, bài thi sẽ bị khóa.</p>
            <button class="nav-btn" onclick="closeWarning()">
                <i class="fas fa-check"></i> Tôi hiểu
            </button>
        </div>
    </div>

    <div class="assignment-container">
        <a href="../python-c.html" class="back-link">
            <i class="fas fa-arrow-left"></i> Quay lại lớp Python - C
        </a>

        <div class="assignment-header">
            <h1>Bài Tập 5: Logic và Quyết Định</h1>
            <p>Boolean, Toán Tử So Sánh và Logic</p>
            <p><strong>50 câu hỏi</strong> - Thời gian: <strong>75 phút</strong></p>
        </div>

        <div class="timer-info">
            <div>
                <i class="fas fa-clock"></i>
                <strong>Thời gian còn lại:</strong>
            </div>
            <div class="timer-display" id="timer">75:00</div>
        </div>

        <div class="progress-container">
            <div class="progress-bar" id="progressBar"></div>
        </div>

        <!-- Quiz Container -->
        <div class="quiz-container" id="quizContainer">
            <div class="question-header">
                <div class="question-number" id="questionNumber">Câu 1/50</div>
                <div>
                    <i class="fas fa-brain"></i>
                    <span id="questionDifficulty">Dễ</span>
                </div>
            </div>

            <div class="question-text" id="questionText">
                <!-- Question will be loaded here -->
            </div>

            <div class="options-container" id="optionsContainer">
                <!-- Options will be loaded here -->
            </div>

            <div class="explanation" id="explanation">
                <!-- Explanation will be shown here -->
            </div>

            <div class="navigation-buttons">
                <button class="nav-btn" id="prevBtn" onclick="previousQuestion()" disabled>
                    <i class="fas fa-chevron-left"></i> Câu trước
                </button>

                <button class="nav-btn" id="nextBtn" onclick="nextQuestion()">
                    Câu tiếp <i class="fas fa-chevron-right"></i>
                </button>

                <button class="submit-btn" id="submitBtn" onclick="submitQuiz()">
                    <i class="fas fa-check-circle"></i> Nộp bài
                </button>
            </div>
        </div>

        <!-- Results Container -->
        <div class="results-container" id="resultsContainer">
            <h2><i class="fas fa-trophy"></i> Kết Quả Bài Thi</h2>

            <div class="score-display" id="finalScore">0/50</div>

            <div class="score-breakdown">
                <div class="score-item">
                    <h3><i class="fas fa-percentage"></i> Điểm Phần Trăm</h3>
                    <p id="percentageScore">0%</p>
                </div>
                <div class="score-item">
                    <h3><i class="fas fa-check-circle"></i> Câu Đúng</h3>
                    <p id="correctCount">0</p>
                </div>
                <div class="score-item">
                    <h3><i class="fas fa-times-circle"></i> Câu Sai</h3>
                    <p id="incorrectCount">0</p>
                </div>
                <div class="score-item">
                    <h3><i class="fas fa-clock"></i> Thời Gian</h3>
                    <p id="completionTime">0 phút</p>
                </div>
            </div>

            <div style="margin-top: 30px;">
                <button class="nav-btn" onclick="restartQuiz()">
                    <i class="fas fa-redo"></i> Làm lại
                </button>
                <a href="../python-c.html" class="nav-btn" style="margin-left: 15px; text-decoration: none;">
                    <i class="fas fa-home"></i> Về lớp học
                </a>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../../../../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, doc, getDoc, setDoc, updateDoc } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Load quiz data
        let quizData = [];
        let currentQuestion = 0;
        let userAnswers = [];
        let startTime = Date.now();
        let timeLimit = 75 * 60 * 1000; // 75 minutes in milliseconds
        let timerInterval;
        let currentUser = null;
        let violationCount = 0;
        let isQuizLocked = false;

        // Anti-cheat system
        let isTabActive = true;

        document.addEventListener('visibilitychange', function() {
            if (document.hidden && !isQuizLocked) {
                isTabActive = false;
                violationCount++;
                document.getElementById('violationCount').textContent = violationCount;
                document.getElementById('warningOverlay').style.display = 'flex';

                if (violationCount >= 3) {
                    lockQuiz();
                }
            } else {
                isTabActive = true;
            }
        });

        window.addEventListener('blur', function() {
            if (!isQuizLocked) {
                isTabActive = false;
                violationCount++;
                document.getElementById('violationCount').textContent = violationCount;
                document.getElementById('warningOverlay').style.display = 'flex';

                if (violationCount >= 3) {
                    lockQuiz();
                }
            }
        });

        window.addEventListener('focus', function() {
            isTabActive = true;
        });

        function lockQuiz() {
            isQuizLocked = true;
            clearInterval(timerInterval);
            alert('Bài thi đã bị khóa do vi phạm quy định quá 3 lần!');
            document.getElementById('quizContainer').style.display = 'none';
            document.getElementById('resultsContainer').innerHTML = `
                <h2 style="color: #dc3545;"><i class="fas fa-lock"></i> Bài Thi Bị Khóa</h2>
                <p>Bài thi của bạn đã bị khóa do vi phạm quy định chống gian lận.</p>
                <p>Vui lòng liên hệ giáo viên để được hỗ trợ.</p>
                <a href="../python-a.html" class="nav-btn">
                    <i class="fas fa-home"></i> Về lớp học
                </a>
            `;
            document.getElementById('resultsContainer').style.display = 'block';
        }

        window.closeWarning = function() {
            document.getElementById('warningOverlay').style.display = 'none';
        };

        // Authentication check
        onAuthStateChanged(auth, (user) => {
            if (user) {
                currentUser = user;
                checkAssignmentStatus();
            } else {
                window.location.href = '../../../../auth/';
            }
        });

        async function checkAssignmentStatus() {
            try {
                const userDoc = await getDoc(doc(db, "users", currentUser.uid));
                if (userDoc.exists()) {
                    const userData = userDoc.data();
                    const assignment5 = userData.assignment5 || {};

                    if (assignment5.completed) {
                        // Show completed status
                        document.getElementById('quizContainer').style.display = 'none';
                        document.getElementById('resultsContainer').innerHTML = `
                            <h2><i class="fas fa-check-circle" style="color: #28a745;"></i> Đã Hoàn Thành</h2>
                            <div class="score-display">${assignment5.score}/50</div>
                            <p>Bạn đã hoàn thành bài tập này với điểm số ${assignment5.score}/50 (${assignment5.percentage}%)</p>
                            <p><strong>Thời gian hoàn thành:</strong> ${assignment5.completionTime}</p>
                            <p><strong>Ngày làm:</strong> ${new Date(assignment5.completedAt).toLocaleDateString('vi-VN')}</p>
                            <div style="margin-top: 30px;">
                                <button class="nav-btn" onclick="showAnswers()">
                                    <i class="fas fa-eye"></i> Xem đáp án
                                </button>
                                <a href="../python-a.html" class="nav-btn" style="margin-left: 15px; text-decoration: none;">
                                    <i class="fas fa-home"></i> Về lớp học
                                </a>
                            </div>
                        `;
                        document.getElementById('resultsContainer').style.display = 'block';
                        return;
                    }
                }

                // Load quiz if not completed
                loadQuizData();
            } catch (error) {
                console.error("Error checking assignment status:", error);
                loadQuizData();
            }
        }

        async function loadQuizData() {
            try {
                const module = await import('./quiz-data-5.js');
                quizData = module.quizData;
                initializeQuiz();
            } catch (error) {
                console.error("Error loading quiz data:", error);
                alert("Không thể tải dữ liệu bài tập. Vui lòng thử lại.");
            }
        }

        function initializeQuiz() {
            // Initialize answers array
            userAnswers = new Array(quizData.length).fill(null);

            // Start timer
            startTimer();

            // Show first question
            showQuestion(0);

            // Create background effects
            createBackgroundEffects();
        }

        function startTimer() {
            const timerElement = document.getElementById('timer');

            timerInterval = setInterval(() => {
                const elapsed = Date.now() - startTime;
                const remaining = timeLimit - elapsed;

                if (remaining <= 0) {
                    clearInterval(timerInterval);
                    submitQuiz();
                    return;
                }

                const minutes = Math.floor(remaining / 60000);
                const seconds = Math.floor((remaining % 60000) / 1000);
                timerElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;

                // Change color when time is running out
                if (remaining < 5 * 60 * 1000) { // Less than 5 minutes
                    timerElement.style.color = '#dc3545';
                } else if (remaining < 10 * 60 * 1000) { // Less than 10 minutes
                    timerElement.style.color = '#ffc107';
                }
            }, 1000);
        }

        function showQuestion(index) {
            if (index < 0 || index >= quizData.length) return;

            currentQuestion = index;
            const question = quizData[index];

            // Update question number and progress
            document.getElementById('questionNumber').textContent = `Câu ${index + 1}/${quizData.length}`;
            document.getElementById('progressBar').style.width = `${((index + 1) / quizData.length) * 100}%`;

            // Set difficulty
            let difficulty = 'Dễ';
            if (index >= 15 && index < 35) difficulty = 'Trung Bình';
            else if (index >= 35) difficulty = 'Khó';
            document.getElementById('questionDifficulty').textContent = difficulty;

            // Show question text
            document.getElementById('questionText').textContent = question.question;

            // Show options
            const optionsContainer = document.getElementById('optionsContainer');
            optionsContainer.innerHTML = '';

            question.options.forEach((option, optionIndex) => {
                const optionElement = document.createElement('div');
                optionElement.className = 'option';
                optionElement.textContent = `${String.fromCharCode(65 + optionIndex)}. ${option}`;
                optionElement.onclick = () => selectOption(optionIndex);

                // Show previous selection
                if (userAnswers[index] === optionIndex) {
                    optionElement.classList.add('selected');
                }

                optionsContainer.appendChild(optionElement);
            });

            // Hide explanation
            document.getElementById('explanation').classList.remove('show');

            // Update navigation buttons
            document.getElementById('prevBtn').disabled = index === 0;
            document.getElementById('nextBtn').style.display = index === quizData.length - 1 ? 'none' : 'block';
            document.getElementById('submitBtn').style.display = index === quizData.length - 1 ? 'block' : 'none';
        }

        function selectOption(optionIndex) {
            userAnswers[currentQuestion] = optionIndex;

            // Update UI
            const options = document.querySelectorAll('.option');
            options.forEach((option, index) => {
                option.classList.remove('selected');
                if (index === optionIndex) {
                    option.classList.add('selected');
                }
            });
        }

        window.previousQuestion = function() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        };

        window.nextQuestion = function() {
            if (currentQuestion < quizData.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        };

        window.submitQuiz = async function() {
            if (isQuizLocked) return;

            const unanswered = userAnswers.filter(answer => answer === null).length;
            if (unanswered > 0) {
                if (!confirm(`Bạn còn ${unanswered} câu chưa trả lời. Bạn có chắc muốn nộp bài?`)) {
                    return;
                }
            }

            clearInterval(timerInterval);

            // Calculate results
            let correctCount = 0;
            userAnswers.forEach((answer, index) => {
                if (answer === quizData[index].correct) {
                    correctCount++;
                }
            });

            const totalQuestions = quizData.length;
            const percentage = Math.round((correctCount / totalQuestions) * 100);
            const completionTime = Math.round((Date.now() - startTime) / 60000);

            // Show results
            document.getElementById('quizContainer').style.display = 'none';
            document.getElementById('finalScore').textContent = `${correctCount}/${totalQuestions}`;
            document.getElementById('percentageScore').textContent = `${percentage}%`;
            document.getElementById('correctCount').textContent = correctCount;
            document.getElementById('incorrectCount').textContent = totalQuestions - correctCount;
            document.getElementById('completionTime').textContent = `${completionTime} phút`;
            document.getElementById('resultsContainer').style.display = 'block';

            // Save to Firebase
            try {
                const userRef = doc(db, "users", currentUser.uid);
                const userDoc = await getDoc(userRef);

                if (userDoc.exists()) {
                    const userData = userDoc.data();
                    const currentTotalScore = userData.totalScore || 0;
                    const currentAssignmentCount = userData.assignmentCount || 0;
                    const currentTotalTime = userData.totalCompletionTime || 0;

                    await updateDoc(userRef, {
                        assignment5: {
                            completed: true,
                            score: correctCount,
                            percentage: percentage,
                            completionTime: `${completionTime} phút`,
                            completedAt: Date.now(),
                            answers: userAnswers,
                            violationCount: violationCount
                        },
                        totalScore: currentTotalScore + correctCount,
                        assignmentCount: currentAssignmentCount + 1,
                        totalCompletionTime: currentTotalTime + (completionTime * 60),
                        lastAssignmentDate: Date.now()
                    });
                }
            } catch (error) {
                console.error("Error saving results:", error);
            }
        };

        window.restartQuiz = function() {
            if (confirm('Bạn có chắc muốn làm lại bài tập? Kết quả hiện tại sẽ bị xóa.')) {
                location.reload();
            }
        };

        window.showAnswers = function() {
            // Implementation for showing answers after completion
            alert('Tính năng xem đáp án sẽ được cập nhật sớm!');
        };

        function createBackgroundEffects() {
            const container = document.getElementById('backgroundEffects');

            // Create shooting stars
            for (let i = 0; i < 3; i++) {
                setTimeout(() => {
                    const star = document.createElement('div');
                    star.className = 'shooting-star';
                    star.style.top = Math.random() * 100 + '%';
                    star.style.animationDelay = Math.random() * 3 + 's';
                    container.appendChild(star);

                    setTimeout(() => {
                        star.remove();
                    }, 3000);
                }, i * 2000);
            }

            // Create floating particles
            for (let i = 0; i < 10; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                container.appendChild(particle);
            }
        }
    </script>
</body>
</html>