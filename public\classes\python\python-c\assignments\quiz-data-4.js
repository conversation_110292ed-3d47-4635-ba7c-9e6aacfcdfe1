// B<PERSON> câu hỏi trắc nghiệm - Bài 4: <PERSON><PERSON> và <PERSON>hập/Xuất Dữ Liệu
const quizData = [
    // MỨC ĐỘ DỄ (Nhận biết & Thông hiểu cơ bản)
    {
        question: "Toán tử nào được sử dụng cho phép cộng trong Python?",
        options: ["&", "ADD", "+", "SUM"],
        correct: 2,
        explanation: "Dấu + được sử dụng cho phép cộng trong Python. & là toán tử bitwise AND, ADD và SUM không phải toán tử Python."
    },
    {
        question: "Toán tử ** trong Python dùng để làm gì?",
        options: ["Phép nhân", "Phép chia", "Phép lũy thừa (mũ)", "Phép chia lấy dư"],
        correct: 2,
        explanation: "Toán tử ** dùng cho phép lũy thừa (mũ). Ví dụ: 2**3 = 8. * là phép nhân, / là phép chia, % là chia lấy dư."
    },
    {
        question: "Toán tử nào dùng để thực hiện phép chia và luôn trả về kết quả là số thực (float)?",
        options: ["\\", "/", "//", "%"],
        correct: 1,
        explanation: "Toán tử / luôn trả về số thực (float). // là chia lấy nguyên, % là chia lấy dư, \\ không phải toán tử Python."
    },
    {
        question: "Hàm nào được sử dụng để nhận dữ liệu nhập vào từ bàn phím của người dùng?",
        options: ["get()", "read()", "type()", "input()"],
        correct: 3,
        explanation: "Hàm input() dùng để nhận dữ liệu từ người dùng. get(), read(), type() có chức năng khác hoặc không tồn tại."
    },
    {
        question: "Hàm input() luôn luôn trả về dữ liệu dưới dạng kiểu nào?",
        options: ["int (số nguyên)", "float (số thực)", "str (chuỗi)", "bool (logic)"],
        correct: 2,
        explanation: "Hàm input() luôn trả về chuỗi (str), dù người dùng nhập số hay chữ. Cần ép kiểu để chuyển sang int hoặc float."
    },
    {
        question: "Hàm nào dùng để chuyển đổi một giá trị thành kiểu số nguyên?",
        options: ["integer()", "to_int()", "int()", "convert.int()"],
        correct: 2,
        explanation: "Hàm int() dùng để chuyển đổi thành số nguyên. Các hàm khác không tồn tại trong Python."
    },
    {
        question: "Hàm nào dùng để chuyển đổi một giá trị thành kiểu số thực?",
        options: ["float()", "to_float()", "real()", "decimal()"],
        correct: 0,
        explanation: "Hàm float() dùng để chuyển đổi thành số thực. Các hàm khác không phải built-in functions của Python."
    },
    {
        question: "Hàm nào dùng để chuyển đổi một giá trị (ví dụ: một số) thành kiểu chuỗi?",
        options: ["string()", "text()", "str()", "to_str()"],
        correct: 2,
        explanation: "Hàm str() dùng để chuyển đổi thành chuỗi. Các hàm khác không tồn tại trong Python."
    },
    {
        question: "Kết quả của biểu thức 10 + 5 là gì?",
        options: ["105", "15", "\"105\"", "Lỗi"],
        correct: 1,
        explanation: "10 + 5 = 15. Đây là phép cộng số học, không phải nối chuỗi."
    },
    {
        question: "Kết quả của biểu thức 5 * 4 là gì?",
        options: ["9", "20", "54", "\"20\""],
        correct: 1,
        explanation: "5 * 4 = 20. Đây là phép nhân số học."
    },
    {
        question: "Trong hàm print(), tham số nào được sử dụng để thay đổi ký tự phân cách giữa các giá trị?",
        options: ["separator", "split", "sep", "char"],
        correct: 2,
        explanation: "Tham số sep trong print() dùng để thay đổi ký tự phân cách. Ví dụ: print('A', 'B', sep='-') in ra A-B."
    },
    {
        question: "Trong hàm print(), tham số nào được sử dụng để thay đổi ký tự kết thúc của dòng?",
        options: ["finish", "last", "end", "stop"],
        correct: 2,
        explanation: "Tham số end trong print() dùng để thay đổi ký tự kết thúc. Mặc định là \\n (xuống dòng)."
    },
    {
        question: "Toán tử % trong Python dùng để làm gì?",
        options: ["Tính phần trăm", "Chia lấy nguyên", "Chia lấy dư (Modulo)", "Chia và làm tròn"],
        correct: 2,
        explanation: "Toán tử % là phép chia lấy dư (modulo). Ví dụ: 10 % 3 = 1 (10 chia 3 dư 1)."
    },
    {
        question: "Toán tử // trong Python dùng để làm gì?",
        options: ["Comment nhiều dòng", "Chia và luôn ra số thực", "Chia lấy phần nguyên", "So sánh bằng"],
        correct: 2,
        explanation: "Toán tử // là phép chia lấy phần nguyên. Ví dụ: 10 // 3 = 3. # dùng cho comment, == dùng so sánh."
    },
    {
        question: "Để chèn một biến vào chuỗi một cách hiện đại, ta có thể đặt chữ cái nào trước dấu nháy của chuỗi?",
        options: ["p (p-string)", "s (s-string)", "f (f-string)", "v (v-string)"],
        correct: 2,
        explanation: "f-string (formatted string literal) cho phép chèn biến vào chuỗi. Ví dụ: f'Tôi {age} tuổi'."
    },
    // MỨC ĐỘ TRUNG BÌNH (Thông hiểu & Vận dụng thấp)
    {
        question: "Kết quả của biểu thức 10 + 5 * 2 là gì?",
        options: ["30", "20", "25", "17"],
        correct: 1,
        explanation: "Theo thứ tự ưu tiên, phép nhân thực hiện trước: 5 * 2 = 10, sau đó 10 + 10 = 20."
    },
    {
        question: "Kết quả của biểu thức (10 + 5) * 2 là gì?",
        options: ["30", "20", "25", "17"],
        correct: 0,
        explanation: "Ngoặc đơn có ưu tiên cao nhất: (10 + 5) = 15, sau đó 15 * 2 = 30."
    },
    {
        question: "Kết quả của 10 / 2 là gì?",
        options: ["5 (kiểu int)", "5.0 (kiểu float)", "2", "Lỗi"],
        correct: 1,
        explanation: "Toán tử / luôn trả về float, ngay cả khi chia hết. 10 / 2 = 5.0 (float)."
    },
    {
        question: "Kết quả của 10 // 3 là gì?",
        options: ["3.333", "3", "1", "4"],
        correct: 1,
        explanation: "Toán tử // chia lấy phần nguyên. 10 chia 3 được 3 dư 1, lấy phần nguyên là 3."
    },
    {
        question: "Kết quả của 10 % 3 là gì?",
        options: ["3.333", "3", "1", "0"],
        correct: 2,
        explanation: "Toán tử % chia lấy dư. 10 chia 3 được 3 dư 1, lấy phần dư là 1."
    },
    {
        question: "Kết quả của 2 ** 4 là gì?",
        options: ["8", "6", "16", "24"],
        correct: 2,
        explanation: "2 ** 4 = 2^4 = 2 * 2 * 2 * 2 = 16."
    },
    {
        question: "Đoạn code age_str = input(\"Tuoi: \") nhận được đầu vào là \"25\". Kiểu dữ liệu của biến age_str là gì?",
        options: ["int", "float", "str", "number"],
        correct: 2,
        explanation: "Hàm input() luôn trả về chuỗi (str), dù người dùng nhập số hay chữ."
    },
    {
        question: "Đoạn code sau sẽ in ra gì? print(\"Hello\", \"World\", sep=\"-\")",
        options: ["Hello World", "Hello-World", "HelloWorld", "Lỗi"],
        correct: 1,
        explanation: "Tham số sep=\"-\" thay thế khoảng trắng mặc định bằng dấu gạch ngang."
    },
    {
        question: "Đoạn code sau sẽ in ra gì?\nprint(\"Python\", end=\" \")\nprint(\"is fun!\")",
        options: ["Python is fun!", "Pythonis fun!", "Python is fun! (trên hai dòng riêng biệt)", "Python"],
        correct: 0,
        explanation: "end=\" \" thay thế ký tự xuống dòng bằng khoảng trắng, nên hai lệnh print in trên cùng một dòng."
    },
    {
        question: "Cho age = 20. Đoạn code print(f\"Toi nam nay {age} tuoi.\") sẽ in ra gì?",
        options: ["Toi nam nay {age} tuoi.", "Toi nam nay 20 tuoi.", "f\"Toi nam nay 20 tuoi.\"", "Lỗi"],
        correct: 1,
        explanation: "f-string cho phép chèn giá trị biến vào chuỗi. {age} được thay thế bằng giá trị 20."
    },
    {
        question: "Để nhận một số nguyên từ người dùng và lưu vào biến num, cách viết nào sau đây là đúng và gọn nhất?",
        options: ["num = input(\"Nhap so: \")", "temp = input(\"Nhap so: \"); num = int(temp)", "num = int(input(\"Nhap so: \"))", "num = str(input(\"Nhap so: \"))"],
        correct: 2,
        explanation: "num = int(input(\"Nhap so: \")) vừa nhận input vừa chuyển đổi thành int trong một dòng."
    },
    {
        question: "Kết quả của type(20 // 4) là gì?",
        options: ["&lt;class 'int'&gt;", "&lt;class 'float'&gt;", "&lt;class 'str'&gt;", "&lt;class 'division'&gt;"],
        correct: 0,
        explanation: "Toán tử // trả về int. 20 // 4 = 5 (int), nên type() trả về &lt;class 'int'&gt;."
    },
    {
        question: "Kết quả của type(20 / 4) là gì?",
        options: ["&lt;class 'int'&gt;", "&lt;class 'float'&gt;", "&lt;class 'str'&gt;", "&lt;class 'division'&gt;"],
        correct: 1,
        explanation: "Toán tử / luôn trả về float. 20 / 4 = 5.0 (float), nên type() trả về &lt;class 'float'&gt;."
    },
    {
        question: "Nếu người dùng nhập vào \"3.14\" khi chương trình chạy lệnh pi = float(input(\"Nhap so PI: \")), giá trị của biến pi sẽ là gì?",
        options: ["Chuỗi \"3.14\"", "Số nguyên 3", "Số thực 3.14", "Lỗi"],
        correct: 2,
        explanation: "float() chuyển đổi chuỗi \"3.14\" thành số thực 3.14."
    },
    {
        question: "Kết quả của 9 % 2 là gì? (Phép toán này thường dùng để làm gì?)",
        options: ["4.5 (Kết quả phép chia)", "4 (Dùng để làm tròn)", "1 (Dùng để kiểm tra số lẻ)", "0 (Dùng để kiểm tra số chẵn)"],
        correct: 2,
        explanation: "9 % 2 = 1. Phép % thường dùng kiểm tra tính chẵn lẻ: số lẻ % 2 = 1, số chẵn % 2 = 0."
    },
    {
        question: "Đoạn code sau sẽ in ra gì? print(\"Ngay\", \"Thang\", \"Nam\", sep=\":\")",
        options: ["Ngay Thang Nam", "Ngay:Thang:Nam", "Ngay,Thang,Nam", "NgayThangNam"],
        correct: 1,
        explanation: "sep=\":\" thay thế khoảng trắng mặc định bằng dấu hai chấm."
    },
    {
        question: "Lệnh print(\"1\" + \"2\") sẽ cho kết quả gì?",
        options: ["3 (kiểu int)", "12 (kiểu int)", "\"12\" (kiểu str)", "\"3\" (kiểu str)"],
        correct: 2,
        explanation: "Toán tử + với chuỗi là phép nối chuỗi. \"1\" + \"2\" = \"12\" (chuỗi)."
    },
    {
        question: "Lệnh print(1 + 2) sẽ cho kết quả gì?",
        options: ["3 (kiểu int)", "12 (kiểu int)", "\"12\" (kiểu str)", "\"3\" (kiểu str)"],
        correct: 0,
        explanation: "Toán tử + với số là phép cộng số học. 1 + 2 = 3 (int)."
    },
    {
        question: "Thứ tự ưu tiên của các phép toán nào sau đây là đúng?",
        options: ["Cộng/Trừ -> Nhân/Chia -> Lũy thừa", "Lũy thừa -> Cộng/Trừ -> Nhân/Chia", "Nhân/Chia -> Lũy thừa -> Cộng/Trừ", "Lũy thừa -> Nhân/Chia -> Cộng/Trừ"],
        correct: 3,
        explanation: "Thứ tự ưu tiên: Lũy thừa (**) cao nhất, sau đó Nhân/Chia (*, /, //, %), cuối cùng Cộng/Trừ (+, -)."
    },
    {
        question: "Mục đích của việc sử dụng f-string là gì?",
        options: ["Để tính toán các giá trị toán học", "Để dễ dàng chèn giá trị của các biến vào bên trong một chuỗi", "Để chuyển đổi kiểu dữ liệu của chuỗi", "Để lặp lại một chuỗi nhiều lần"],
        correct: 1,
        explanation: "f-string giúp chèn giá trị biến vào chuỗi một cách dễ đọc và hiệu quả."
    },
    // MỨC ĐỘ KHÓ (Vận dụng cao & Phân tích, Đánh giá)
    {
        question: "Cho x = \"100\". Đoạn code print(\"Gia tri la: \" + x) sẽ in ra gì?",
        options: ["Lỗi (TypeError)", "Gia tri la: 100", "Gia tri la: x", "100"],
        correct: 1,
        explanation: "x là chuỗi \"100\", nên có thể nối với chuỗi khác. Kết quả: \"Gia tri la: 100\"."
    },
    {
        question: "Cho y = 50. Đoạn code print(\"Gia tri la: \" + y) sẽ in ra gì?",
        options: ["Lỗi (TypeError)", "Gia tri la: 50", "Gia tri la: y", "50"],
        correct: 0,
        explanation: "y là số (int), không thể nối trực tiếp với chuỗi. Sẽ gây lỗi TypeError."
    },
    {
        question: "Để sửa lỗi trong câu 37, cần viết lại lệnh print như thế nào?",
        options: ["print(\"Gia tri la: \", str(y))", "print(\"Gia tri la: \" + str(y))", "print(f\"Gia tri la: {y}\")", "Tất cả các đáp án trên đều đúng"],
        correct: 3,
        explanation: "Cả 3 cách đều đúng: dùng dấu phẩy, str() để nối, hoặc f-string."
    },
    {
        question: "Khi chương trình chạy lệnh num = int(input(\"Nhap so: \")) và người dùng nhập vào chữ \"hello\", điều gì sẽ xảy ra?",
        options: ["num sẽ có giá trị là \"hello\"", "num sẽ có giá trị là 0", "Chương trình sẽ bị lỗi (ValueError)", "Chương trình sẽ hỏi lại cho đến khi người dùng nhập số"],
        correct: 2,
        explanation: "int() không thể chuyển đổi \"hello\" thành số, sẽ gây lỗi ValueError."
    },
    {
        question: "Khi chương trình chạy lệnh num = int(input(\"Nhap so: \")) và người dùng nhập vào số \"12.5\", điều gì sẽ xảy ra?",
        options: ["num sẽ có giá trị là 12", "num sẽ có giá trị là 12.5", "Chương trình sẽ bị lỗi (ValueError)", "num sẽ có giá trị là 13"],
        correct: 2,
        explanation: "int() không thể chuyển đổi trực tiếp \"12.5\" (chuỗi số thực) thành int, sẽ gây lỗi ValueError."
    },
    {
        question: "Kết quả của biểu thức 16 / 2 // 3 là gì?",
        options: ["2.666...", "2.0", "2", "Lỗi"],
        correct: 1,
        explanation: "Thứ tự từ trái sang phải: 16 / 2 = 8.0, sau đó 8.0 // 3 = 2.0 (float)."
    },
    {
        question: "Kết quả của biểu thức 5 ** 2 % 4 là gì?",
        options: ["2", "2.5", "1", "0"],
        correct: 2,
        explanation: "Lũy thừa trước: 5 ** 2 = 25, sau đó 25 % 4 = 1 (25 chia 4 dư 1)."
    },
    {
        question: "Đoạn code sau sẽ in ra gì?\nx = 1\ny = 2\nprint(x, y, sep=\"\", end=\"\")\nprint(x + y)",
        options: ["123", "1 2 3", "12 (trên một dòng) và 3 (trên dòng tiếp theo)", "Lỗi"],
        correct: 0,
        explanation: "Dòng 1: in \"12\" (sep=\"\" nối liền, end=\"\" không xuống dòng). Dòng 2: in \"3\" ngay sau. Kết quả: \"123\"."
    },
    {
        question: "Để in ra A-B-C trên một dòng, cách viết nào sau đây là KHÔNG đúng?",
        options: ["print(\"A\", \"B\", \"C\", sep=\"-\")", "print(\"A-\" + \"B-\" + \"C\")", "print(\"A-B-C\")", "print(\"A\", \"-B\", \"-C\")"],
        correct: 3,
        explanation: "Đáp án D sẽ in \"A -B -C\" (có khoảng trắng thừa), không phải \"A-B-C\"."
    },
    {
        question: "Cho a = 5, b = 2. Biểu thức f\"{a} / {b} = {a/b}\" sẽ tạo ra chuỗi nào?",
        options: ["f\"5 / 2 = 2.5\"", "\"a / b = a/b\"", "\"5 / 2 = 2.5\"", "Lỗi"],
        correct: 2,
        explanation: "f-string thay thế {a}, {b}, {a/b} bằng giá trị thực: \"5 / 2 = 2.5\"."
    },
    {
        question: "Đoạn code sau dùng để tính diện tích hình tròn. Dòng nào chứa lỗi?\n# Dong 1\npi = \"3.14\"\n# Dong 2\nr_str = input(\"Nhap ban kinh: \")\n# Dong 3\nr = int(r_str)\n# Dong 4\ndien_tich = pi * (r ** 2)\n# Dong 5\nprint(dien_tich)",
        options: ["Dòng 1 và 3", "Dòng 3 và 4", "Dòng 1 và 4", "Dòng 2 và 5"],
        correct: 2,
        explanation: "Dòng 1: pi nên là số (3.14), không phải chuỗi. Dòng 4: không thể nhân chuỗi với số."
    },
    {
        question: "Để sửa lỗi trong câu 46, cần làm gì?",
        options: ["Ở Dòng 1, pi = 3.14. Ở Dòng 4, không cần thay đổi.", "Ở Dòng 1, pi = 3.14. Ở Dòng 3, r = float(r_str). Ở Dòng 4, phép * là đúng.", "Ở Dòng 1, pi = float(pi). Ở Dòng 3, r = float(r_str).", "Chỉ cần thay đổi Dòng 3 thành r = float(r_str)."],
        correct: 1,
        explanation: "Cần sửa pi thành số (3.14) và r thành float để tính toán chính xác với số thập phân."
    },
    {
        question: "Lệnh print(\"*\" * 5, end=\"\\n\\n\") sẽ làm gì?",
        options: ["In ra 5 dấu * và xuống 1 dòng.", "In ra 5 dấu * và xuống 2 dòng.", "In ra 5 dấu * và thêm 2 ký tự n vào cuối.", "Gây lỗi cú pháp."],
        correct: 1,
        explanation: "\"*\" * 5 tạo \"*****\", end=\"\\n\\n\" thêm 2 ký tự xuống dòng."
    },
    {
        question: "Cho x = 1, y = \"2\", z = 3. Lệnh nào sau đây sẽ gây ra lỗi?",
        options: ["print(str(x) + y + str(z))", "print(x, y, z)", "print(f\"{x}{y}{z}\")", "print(x + z + y)"],
        correct: 3,
        explanation: "x + z + y = 1 + 3 + \"2\" = 4 + \"2\" gây lỗi TypeError (không thể cộng int với str)."
    },
    {
        question: "Mục đích chính của việc ép kiểu str sang int hoặc float là gì?",
        options: ["Để chuỗi đó có thể được nối với chuỗi khác.", "Để có thể thực hiện các phép tính toán học trên giá trị đó.", "Để tiết kiệm bộ nhớ máy tính.", "Để làm cho chuỗi dễ đọc hơn."],
        correct: 1,
        explanation: "Ép kiểu từ str sang int/float để có thể thực hiện các phép toán số học như +, -, *, /, etc."
    }
];

// Export for use in quiz system
if (typeof module !== 'undefined' && module.exports) {
    module.exports = quizData;
}

export default quizData;
