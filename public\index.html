<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vthon - <PERSON><PERSON><PERSON> học lập trình <PERSON> và AI</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="assets/images/favicon.png">
    <link rel="shortcut icon" type="image/png" href="assets/images/favicon.png">
    <link rel="apple-touch-icon" href="assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="114x114" href="assets/images/favicon.png">


    <link rel="stylesheet" href="assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- GSAP Animation Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/TextPlugin.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="index.html" class="active">Trang Chủ</a></li>
                    <li><a href="classes/">Lớp Học</a></li>
                    <li><a href="achievements/">Thành Tích</a></li>
                    <li><a href="auth/register.html">Đăng Ký</a></li>
                    <li><a href="rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="research/">Sự kiện</a></li>
                    <li><a href="auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Hero Section with Full Screen Background -->
    <section class="hero">
        <div class="hero-content">
            <div class="typing-container">
                <h1 class="typing-text">Chào mừng bạn đến với VThon Academy</h1>
            </div>
        </div>

        <!-- Scroll Indicator - Moved outside hero-content -->
        <div class="scroll-indicator" data-aos="fade-up" data-aos-delay="4000">
            <p class="scroll-text">Khám phá thế giới Python và AI cùng chúng tôi</p>
            <div class="scroll-arrow">
                <i class="fas fa-chevron-down"></i>
            </div>
        </div>

        <!-- Floating Elements -->
        <div class="floating-elements">
            <div class="floating-element" style="--delay: 0s; --duration: 3s;">💻</div>
            <div class="floating-element" style="--delay: 1s; --duration: 4s;">🐍</div>
            <div class="floating-element" style="--delay: 2s; --duration: 3.5s;">🤖</div>
            <div class="floating-element" style="--delay: 0.5s; --duration: 4.5s;">📚</div>
            <div class="floating-element" style="--delay: 1.5s; --duration: 3.2s;">⚡</div>
        </div>
    </section>

    <!-- Events Overview Section -->
    <section class="events-overview" id="events">
        <div class="container">
            <div class="events-header" data-aos="fade-up">
                <h2 class="events-title">
                    <span class="gradient-text">Sự Kiện Nổi Bật</span>
                    <div class="title-decoration">
                        <div class="decoration-line"></div>
                        <i class="fas fa-calendar-star"></i>
                        <div class="decoration-line"></div>
                    </div>
                </h2>
                <p class="events-subtitle">Khám phá các sự kiện thú vị và cơ hội thể hiện tài năng lập trình</p>
            </div>

            <div class="events-grid">
                <!-- Code Camp Hè 2025 -->
                <div class="event-card summer-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="event-card-header">
                        <div class="event-badge active">
                            <i class="fas fa-fire"></i>
                            <span>ĐANG DIỄN RA</span>
                        </div>
                        <div class="event-icon">🔥</div>
                    </div>
                    <div class="event-card-content">
                        <h3 class="event-card-title">Code Camp Hè 2025</h3>
                        <p class="event-card-subtitle">Cuộc thi lập trình hè dành cho học viên Python</p>
                        <div class="event-card-info">
                            <div class="info-item">
                                <i class="fas fa-calendar"></i>
                                <span>14/06 - 01/08/2025</span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-trophy"></i>
                                <span>Giải thưởng 300K VNĐ</span>
                            </div>
                        </div>
                    </div>
                    <div class="event-card-footer">
                        <a href="rankings/" class="btn btn-event-card">
                            <i class="fas fa-chart-line"></i>
                            Xem Bảng Xếp Hạng
                        </a>
                    </div>
                </div>

                <!-- Farm Code Challenge 2026 -->
                <div class="event-card farm-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="event-card-header">
                        <div class="event-badge upcoming">
                            <i class="fas fa-seedling"></i>
                            <span>SẮP DIỄN RA</span>
                        </div>
                        <div class="event-icon">🌾</div>
                    </div>
                    <div class="event-card-content">
                        <h3 class="event-card-title">Farm Code Challenge</h3>
                        <p class="event-card-subtitle">Lập trình điều khiển drone nông nghiệp thông minh</p>
                        <div class="event-card-info">
                            <div class="info-item">
                                <i class="fas fa-calendar"></i>
                                <span>2026</span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-trophy"></i>
                                <span>Giải thưởng 450K VNĐ</span>
                            </div>
                        </div>
                    </div>
                    <div class="event-card-footer">
                        <a href="research/#farm-challenge" class="btn btn-event-card">
                            <i class="fas fa-info-circle"></i>
                            Xem Chi Tiết
                        </a>
                    </div>
                </div>

                <!-- Xuân Scratch Game Festival -->
                <div class="event-card tet-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="event-card-header">
                        <div class="event-badge upcoming">
                            <i class="fas fa-calendar-plus"></i>
                            <span>SẮP DIỄN RA</span>
                        </div>
                        <div class="event-icon">🧧</div>
                    </div>
                    <div class="event-card-content">
                        <h3 class="event-card-title">Xuân Scratch Festival</h3>
                        <p class="event-card-subtitle">Lễ hội game Scratch chào đón Tết Nguyên Đán</p>
                        <div class="event-card-info">
                            <div class="info-item">
                                <i class="fas fa-calendar"></i>
                                <span>Xuân 2026</span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-gamepad"></i>
                                <span>Scratch Games</span>
                            </div>
                        </div>
                    </div>
                    <div class="event-card-footer">
                        <a href="https://scratch.mit.edu/" class="btn btn-event-card" target="_blank">
                            <i class="fas fa-play"></i>
                            Tiến hành làm
                        </a>
                    </div>
                </div>

                <!-- Chạm Yêu Thương -->
                <div class="event-card love-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="event-card-header">
                        <div class="event-badge upcoming">
                            <i class="fas fa-heart"></i>
                            <span>SẮP DIỄN RA</span>
                        </div>
                        <div class="event-icon">💖</div>
                    </div>
                    <div class="event-card-content">
                        <h3 class="event-card-title">Chạm Yêu Thương</h3>
                        <p class="event-card-subtitle">Thiết kế thiệp chúc mừng ngày Phụ nữ Việt Nam</p>
                        <div class="event-card-info">
                            <div class="info-item">
                                <i class="fas fa-calendar"></i>
                                <span>20/10/2025</span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-heart"></i>
                                <span>Scratch Design</span>
                            </div>
                        </div>
                    </div>
                    <div class="event-card-footer">
                        <a href="https://scratch.mit.edu/" class="btn btn-event-card" target="_blank">
                            <i class="fas fa-play"></i>
                            Tiến hành làm
                        </a>
                    </div>
                </div>
            </div>

            <!-- View All Events CTA -->
            <div class="events-cta" data-aos="fade-up" data-aos-delay="500">
                <a href="research/" class="btn btn-primary">
                    <i class="fas fa-calendar-alt"></i>
                    Xem Tất Cả Sự Kiện
                </a>
            </div>
        </div>

        <!-- Animated Background Elements -->
        <div class="events-bg-elements">
            <div class="bg-element trophy" style="--delay: 0s;">🏆</div>
            <div class="bg-element code" style="--delay: 1s;">💻</div>
            <div class="bg-element fire" style="--delay: 2s;">🔥</div>
            <div class="bg-element star" style="--delay: 0.5s;">⭐</div>
            <div class="bg-element rocket" style="--delay: 1.5s;">🚀</div>
            <div class="bg-element heart" style="--delay: 2.5s;">💖</div>
        </div>
    </section>

    <!-- Courses Section -->
    <section class="courses-section" id="courses">
        <div class="container">
            <div class="courses-header" data-aos="fade-up">
                <h2 class="courses-title">
                    <span class="gradient-text">Khóa Học Của Chúng Tôi</span>
                    <div class="title-decoration">
                        <div class="decoration-line"></div>
                        <i class="fas fa-graduation-cap"></i>
                        <div class="decoration-line"></div>
                    </div>
                </h2>
                <p class="courses-subtitle">Chọn khóa học phù hợp với bạn và bắt đầu hành trình học tập</p>
            </div>

            <div class="courses-grid">
                <!-- Python-AI Course -->
                <div class="course-card python-course" data-aos="fade-up" data-aos-delay="100">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fab fa-python"></i>
                        </div>
                        <div class="course-badge">HOT</div>
                    </div>
                    <div class="course-content">
                        <h3 class="course-title">Python - AI từ Cơ Bản đến Nâng Cao</h3>
                        <div class="course-details">
                            <div class="detail-item">
                                <i class="fas fa-users"></i>
                                <span>Học sinh THCS - THPT</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-money-bill-wave"></i>
                                <span>250,000 VNĐ/tháng</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-clock"></i>
                                <span>8 buổi/tháng, 90 phút/buổi</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-video"></i>
                                <span>Online qua Google Meet</span>
                            </div>
                            <div class="trial-offer">
                                <i class="fas fa-gift"></i>
                                <span>Học thử 2 buổi miễn phí</span>
                            </div>
                        </div>
                        <div class="course-features">
                            <span class="feature-tag">🐍 Python</span>
                            <span class="feature-tag">🤖 AI/ML</span>
                            <span class="feature-tag">💻 Coding</span>
                        </div>
                    </div>
                    <div class="course-footer">
                        <a href="auth/register.html" class="btn btn-course">Đăng Ký Học Thử</a>
                    </div>
                </div>

                <!-- Scratch Course -->
                <div class="course-card scratch-course" data-aos="fade-up" data-aos-delay="200">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fas fa-puzzle-piece"></i>
                        </div>
                        <div class="course-badge">NEW</div>
                    </div>
                    <div class="course-content">
                        <h3 class="course-title">Scratch - Tin Học Cơ Bản</h3>
                        <div class="course-details">
                            <div class="detail-item">
                                <i class="fas fa-users"></i>
                                <span>Học sinh Tiểu học</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-money-bill-wave"></i>
                                <span>300,000 VNĐ/tháng</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-clock"></i>
                                <span>8 buổi/tháng, 90 phút/buổi</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-video"></i>
                                <span>Online qua Google Meet</span>
                            </div>
                            <div class="trial-offer">
                                <i class="fas fa-gift"></i>
                                <span>Học thử 2 buổi miễn phí</span>
                            </div>
                        </div>
                        <div class="course-features">
                            <span class="feature-tag">🎨 Scratch</span>
                            <span class="feature-tag">📊 Excel</span>
                            <span class="feature-tag">📝 Word</span>
                            <span class="feature-tag">🖼️ Canva</span>
                        </div>
                    </div>
                    <div class="course-footer">
                        <a href="auth/register.html" class="btn btn-course">Đăng Ký Học Thử</a>
                    </div>
                </div>

                <!-- STEM Course -->
                <div class="course-card stem-course" data-aos="fade-up" data-aos-delay="300">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fas fa-microscope"></i>
                        </div>
                        <div class="course-badge">PRO</div>
                    </div>
                    <div class="course-content">
                        <h3 class="course-title">Hỗ Trợ Nghiên Cứu KHKT - STEM</h3>
                        <div class="course-details">
                            <div class="detail-item">
                                <i class="fas fa-users"></i>
                                <span>Học sinh THCS - THPT</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-money-bill-wave"></i>
                                <span>350,000 VNĐ/tháng</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-clock"></i>
                                <span>8 buổi/tháng, 90 phút/buổi</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-video"></i>
                                <span>Online qua Google Meet</span>
                            </div>
                            <div class="detail-item special">
                                <i class="fas fa-star"></i>
                                <span>4 buổi code + 4 buổi slides</span>
                            </div>
                            <div class="trial-offer">
                                <i class="fas fa-gift"></i>
                                <span>Học thử 2 buổi miễn phí</span>
                            </div>
                        </div>
                        <div class="course-features">
                            <span class="feature-tag">🔬 KHKT</span>
                            <span class="feature-tag">📊 STEM</span>
                            <span class="feature-tag">📋 Báo cáo</span>
                            <span class="feature-tag">🏆 Dự án</span>
                        </div>
                    </div>
                    <div class="course-footer">
                        <a href="auth/register.html" class="btn btn-course">Đăng Ký Học Thử</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Animated Background Elements -->
        <div class="courses-bg-elements">
            <div class="bg-element book" style="--delay: 0s;">📚</div>
            <div class="bg-element graduation" style="--delay: 1s;">🎓</div>
            <div class="bg-element lightbulb" style="--delay: 2s;">💡</div>
            <div class="bg-element computer" style="--delay: 0.5s;">💻</div>
            <div class="bg-element science" style="--delay: 1.5s;">🔬</div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script type="module">
        // Import the functions you need from the SDKs
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-analytics.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const auth = getAuth(app);

        // Check if user is logged in
        onAuthStateChanged(auth, (user) => {
            const loginButton = document.querySelector('.hero .btn');

            // Only update if button exists
            if (loginButton) {
                if (user) {
                    // User is logged in
                    loginButton.textContent = 'Vào Trang Cá Nhân';
                } else {
                    // User is not logged in
                    loginButton.textContent = 'Đăng Nhập';
                }
            }
        });
    </script>

    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });
    </script>

    <!-- Scroll Indicator Script -->
    <script>
        // Smooth scroll to next section when clicking scroll indicator
        document.querySelector('.scroll-arrow').addEventListener('click', function() {
            const nextSection = document.querySelector('#code-camp');
            if (nextSection) {
                nextSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    </script>

    <!-- AOS Script -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <script src="assets/js/script.js"></script>
    <script src="assets/js/gsap-animations.js"></script></script>
</body>
</html>